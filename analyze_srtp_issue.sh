#!/bin/bash

echo "=== SRTP 问题深度分析 ==="
echo "时间: $(date)"
echo ""

CLIENT_IP="***********"
SERVER_IP="*************"

# 1. 分析 DTLS 握手过程
echo "1. DTLS 握手分析"
echo "================"

echo "开始监控 DTLS 握手过程..."
echo "请在客户端 ($CLIENT_IP) 开始 WebRTC 连接"
echo ""

# 监控 DTLS 握手
timeout 30 tcpdump -i any -v -X "host $CLIENT_IP and udp and portrange 20000-40000" 2>/dev/null | while read line; do
    # 检查 DTLS 握手消息
    if echo "$line" | grep -qE "Client Hello|Server Hello|Certificate|Finished"; then
        echo "DTLS: $line"
    fi
    
    # 检查 SRTP 相关
    if echo "$line" | grep -qE "SRTP|RTP"; then
        echo "SRTP: $line"
    fi
    
    # 检查错误
    if echo "$line" | grep -qE "error|fail|timeout"; then
        echo "ERROR: $line"
    fi
done &

MONITOR_PID=$!

echo ""
echo "2. 实时 Janus 日志监控"
echo "======================"

# 同时监控 Janus 日志中的 SRTP 错误
if [ -f "/opt/janus/janus.log" ]; then
    tail -f /opt/janus/janus.log | grep -E "SRTP|DTLS|ice|srtp_err" &
    LOG_PID=$!
elif pgrep -f janus > /dev/null; then
    echo "Janus 正在运行，但找不到日志文件"
    echo "请检查 Janus 的标准输出"
fi

echo ""
echo "3. 网络连接状态监控"
echo "=================="

# 监控网络连接状态
while [ $MONITOR_PID ] && kill -0 $MONITOR_PID 2>/dev/null; do
    echo "--- $(date +%H:%M:%S) ---"
    
    # 检查活跃的 RTP 连接
    RTP_CONNECTIONS=$(netstat -an | grep -E ":2[0-9][0-9][0-9][0-9].*$CLIENT_IP" | wc -l)
    echo "活跃 RTP 连接数: $RTP_CONNECTIONS"
    
    if [ $RTP_CONNECTIONS -gt 0 ]; then
        echo "RTP 连接详情:"
        netstat -an | grep -E ":2[0-9][0-9][0-9][0-9].*$CLIENT_IP" | head -3
    fi
    
    # 检查 TURN 连接
    TURN_CONNECTIONS=$(netstat -an | grep -E ":3478.*$CLIENT_IP" | wc -l)
    echo "TURN 连接数: $TURN_CONNECTIONS"
    
    sleep 5
done

# 清理
kill $MONITOR_PID 2>/dev/null
[ ! -z "$LOG_PID" ] && kill $LOG_PID 2>/dev/null

echo ""
echo "4. SRTP 错误模式分析"
echo "=================="

echo "常见 SRTP 错误原因:"
echo "1. srtp_err_status_fail (1) - 通用失败"
echo "   - 密钥协商失败"
echo "   - 包格式错误"
echo "   - 序列号问题"
echo ""
echo "2. DTLS 握手失败"
echo "   - 证书验证失败"
echo "   - 加密套件不匹配"
echo "   - 网络超时"
echo ""
echo "3. ICE 候选者问题"
echo "   - NAT 穿透失败"
echo "   - 防火墙阻止"
echo "   - 网络路由问题"

echo ""
echo "5. 建议的调试步骤"
echo "================"

echo "基于抓包分析，请检查:"
echo ""
echo "A. 如果看到 DTLS Client Hello 但没有 Server Hello:"
echo "   - 服务器端口被阻止"
echo "   - 防火墙丢弃包"
echo ""
echo "B. 如果 DTLS 握手完成但 SRTP 失败:"
echo "   - 检查 libsrtp 版本兼容性"
echo "   - 检查加密算法支持"
echo ""
echo "C. 如果没有看到任何 RTP 包:"
echo "   - ICE 连接失败"
echo "   - 媒体协商问题"
echo ""
echo "D. 如果包大小异常 (太小或太大):"
echo "   - MTU 问题"
echo "   - 分片问题"

echo ""
echo "6. 下一步行动"
echo "============="

echo "请提供以下信息进行进一步分析:"
echo "1. 上述监控的输出结果"
echo "2. 客户端浏览器的 WebRTC 内部统计"
echo "   - Chrome: chrome://webrtc-internals/"
echo "   - Firefox: about:webrtc"
echo "3. 具体的错误时间点和对应的网络包"

echo ""
echo "=== 分析脚本完成 ==="
