<!DOCTYPE html>
<html>
<head>
  <meta charset="UTF-8">
  <title>VideoRoom - 单向连接问题修复版</title>
  <style>
    body { font-family: Arial, sans-serif; margin: 20px; }
    .video-container { display: flex; gap: 20px; margin: 20px 0; }
    video { width: 320px; height: 240px; border: 1px solid #ccc; }
    .controls { margin: 20px 0; }
    button { padding: 10px 20px; margin: 5px; }
    .status { margin: 10px 0; padding: 10px; border-radius: 3px; }
    .success { background-color: #d4edda; color: #155724; }
    .error { background-color: #f8d7da; color: #721c24; }
    .warning { background-color: #fff3cd; color: #856404; }
    .info { background-color: #d1ecf1; color: #0c5460; }
    #log { height: 400px; overflow-y: auto; border: 1px solid #ccc; padding: 10px; background: #f9f9f9; font-family: monospace; font-size: 12px; }
  </style>
</head>
<body>
  <h1>VideoRoom测试 - 单向连接问题修复版</h1>
  
  <div class="controls">
    <button onclick="startVideo()">开始视频</button>
    <button onclick="joinRoom()">加入房间</button>
    <button onclick="leaveRoom()">离开房间</button>
    <button onclick="clearLog()">清空日志</button>
  </div>

  <div id="status" class="status info">准备就绪 - 此版本专门修复单向连接问题</div>

  <div class="video-container">
    <div>
      <h3>本地视频</h3>
      <video id="localVideo" autoplay muted></video>
    </div>
    <div>
      <h3>远程视频</h3>
      <video id="remoteVideo" autoplay></video>
    </div>
  </div>

  <div>
    <h3>详细日志</h3>
    <div id="log"></div>
  </div>

  <script>
    let janus = null;
    let videoroom = null;
    let localStream = null;
    let myid = null;
    let myroom = 1234;
    let remoteFeed = null;

    function log(message, type = 'info') {
      const logDiv = document.getElementById('log');
      const timestamp = new Date().toLocaleTimeString();
      const className = type === 'error' ? 'error' : type === 'warning' ? 'warning' : type === 'success' ? 'success' : 'info';
      logDiv.innerHTML += `<div class="${className}">[${timestamp}] ${message}</div>`;
      logDiv.scrollTop = logDiv.scrollHeight;
      console.log(message);
    }

    function updateStatus(message, type = 'info') {
      const statusDiv = document.getElementById('status');
      statusDiv.className = `status ${type}`;
      statusDiv.textContent = message;
      log(message, type);
    }

    function clearLog() {
      document.getElementById('log').innerHTML = '';
    }

    async function startVideo() {
      try {
        updateStatus('正在获取摄像头权限...', 'info');
        
        localStream = await navigator.mediaDevices.getUserMedia({
          video: { width: 320, height: 240 },
          audio: true
        });
        
        document.getElementById('localVideo').srcObject = localStream;
        updateStatus('摄像头已启动', 'success');
        
        // 初始化Janus
        initJanus();
        
      } catch (error) {
        updateStatus(`获取摄像头失败: ${error.message}`, 'error');
      }
    }

    function initJanus() {
      updateStatus('正在连接Janus服务器...', 'info');
      
      // 针对单向连接问题的特殊ICE配置
      const iceServers = [
        // 优先使用TURN服务器
        { urls: "turn:60.255.197.32:3478", username: "hhzt", credential: "hhzt20130403" },
        { urls: "turn:60.255.197.32:3479", username: "hhzt", credential: "hhzt20130403" },
        // 备用STUN服务器
        { urls: "stun:stun.l.google.com:19302" },
        { urls: "stun:60.255.197.32:3478" }
      ];

      Janus.init({
        debug: "all",
        callback: function() {
          janus = new Janus({
            server: "ws://60.255.197.32:8188/",
            iceServers: iceServers,
            // 强制使用TURN中继解决单向连接问题
            iceTransportPolicy: "relay",
            iceCandidatePoolSize: 10,
            // 增加连接超时时间
            iceConnectionTimeout: 30000,
            success: function() {
              updateStatus('Janus连接成功', 'success');
              attachVideoRoom();
            },
            error: function(error) {
              updateStatus(`Janus连接失败: ${error}`, 'error');
            },
            destroyed: function() {
              updateStatus('Janus连接已断开', 'warning');
            }
          });
        }
      });
    }

    function attachVideoRoom() {
      updateStatus('正在连接VideoRoom插件...', 'info');
      
      janus.attach({
        plugin: "janus.plugin.videoroom",
        opaqueId: "videoroomtest-" + Janus.randomString(12),
        success: function(pluginHandle) {
          videoroom = pluginHandle;
          updateStatus('VideoRoom插件连接成功', 'success');
          log("VideoRoom插件句柄: " + videoroom.getPlugin() + " " + videoroom.getId());
        },
        error: function(error) {
          updateStatus(`VideoRoom插件连接失败: ${error}`, 'error');
        },
        iceState: function(state) {
          log(`ICE状态: ${state}`);
          if (state === 'connected') {
            updateStatus('ICE连接已建立', 'success');
          } else if (state === 'failed') {
            updateStatus('ICE连接失败，尝试重新连接...', 'error');
            // 自动重试机制
            setTimeout(() => {
              if (videoroom) {
                log('尝试重新建立ICE连接...');
                videoroom.hangup();
                setTimeout(attachVideoRoom, 2000);
              }
            }, 3000);
          }
        },
        mediaState: function(medium, on) {
          log(`媒体状态变化: ${medium} ${on ? '开启' : '关闭'}`);
        },
        webrtcState: function(on) {
          log(`WebRTC状态: ${on ? '已连接' : '已断开'}`);
          if (!on && remoteFeed) {
            log('WebRTC连接断开，尝试重新订阅...');
            setTimeout(() => {
              // 重新订阅远程流
              if (myid) {
                log('重新查找发布者...');
                videoroom.send({message: {request: "listparticipants", room: myroom}});
              }
            }, 2000);
          }
        },
        onmessage: function(msg, jsep) {
          log("收到消息: " + JSON.stringify(msg));
          
          const event = msg["videoroom"];
          if (event) {
            if (event === "joined") {
              myid = msg["id"];
              updateStatus(`已加入房间，ID: ${myid}`, 'success');
              
              // 发布本地流
              publishOwnFeed();
              
              if (msg["publishers"]) {
                const list = msg["publishers"];
                for (let f in list) {
                  const id = list[f]["id"];
                  const display = list[f]["display"];
                  log(`发现发布者: ${display} (${id})`);
                  subscribeToFeed(id, display);
                }
              }
            } else if (event === "event") {
              if (msg["publishers"]) {
                const list = msg["publishers"];
                for (let f in list) {
                  const id = list[f]["id"];
                  const display = list[f]["display"];
                  log(`新发布者加入: ${display} (${id})`);
                  subscribeToFeed(id, display);
                }
              }
            } else if (event === "participants") {
              // 处理参与者列表
              if (msg["participants"]) {
                const list = msg["participants"];
                for (let f in list) {
                  const participant = list[f];
                  if (participant["id"] !== myid && participant["publisher"]) {
                    log(`重新发现发布者: ${participant["display"]} (${participant["id"]})`);
                    subscribeToFeed(participant["id"], participant["display"]);
                  }
                }
              }
            }
          }
          
          if (jsep) {
            log("处理SDP: " + jsep.type);
            videoroom.handleRemoteJsep({ jsep: jsep });
          }
        },
        onlocalstream: function(stream) {
          log("本地流已准备");
          // 本地流已经在startVideo中设置
        },
        onremotestream: function(stream) {
          log("收到远程流");
          document.getElementById('remoteVideo').srcObject = stream;
          updateStatus('远程视频流已连接', 'success');
        },
        oncleanup: function() {
          log("清理WebRTC连接");
        }
      });
    }

    function joinRoom() {
      if (!videoroom) {
        updateStatus('请先启动视频', 'warning');
        return;
      }
      
      updateStatus('正在加入房间...', 'info');
      
      const register = {
        request: "join",
        room: myroom,
        ptype: "publisher",
        display: "用户_" + Janus.randomString(4)
      };
      
      videoroom.send({ message: register });
    }

    function publishOwnFeed() {
      log('开始发布本地流...');
      videoroom.createOffer({
        media: { 
          audioRecv: false, 
          videoRecv: false, 
          audioSend: true, 
          videoSend: true 
        },
        stream: localStream,
        success: function(jsep) {
          log("发布本地流成功");
          const publish = { request: "configure", audio: true, video: true };
          videoroom.send({ message: publish, jsep: jsep });
        },
        error: function(error) {
          updateStatus(`发布失败: ${error}`, 'error');
        }
      });
    }

    function subscribeToFeed(id, display) {
      log(`开始订阅用户 ${display} (${id}) 的流`);
      
      janus.attach({
        plugin: "janus.plugin.videoroom",
        opaqueId: "videoroomlistener-" + Janus.randomString(12),
        success: function(pluginHandle) {
          remoteFeed = pluginHandle;
          log(`订阅插件连接成功，开始加入房间...`);
          
          const subscribe = {
            request: "join",
            room: myroom,
            ptype: "subscriber",
            feed: id
          };
          
          remoteFeed.send({ message: subscribe });
        },
        error: function(error) {
          log(`订阅失败: ${error}`, 'error');
        },
        iceState: function(state) {
          log(`订阅ICE状态: ${state}`);
          if (state === 'failed') {
            log('订阅ICE连接失败，尝试重新订阅...');
            setTimeout(() => {
              subscribeToFeed(id, display);
            }, 3000);
          }
        },
        webrtcState: function(on) {
          log(`订阅WebRTC状态: ${on ? '已连接' : '已断开'}`);
        },
        onmessage: function(msg, jsep) {
          log(`订阅消息: ${JSON.stringify(msg)}`);
          const event = msg["videoroom"];
          if (event === "attached") {
            log(`已成功订阅用户 ${display}`);
          }
          
          if (jsep) {
            log(`处理订阅SDP: ${jsep.type}`);
            remoteFeed.createAnswer({
              jsep: jsep,
              media: { audioSend: false, videoSend: false },
              success: function(jsep) {
                log('订阅应答创建成功');
                const body = { request: "start", room: myroom };
                remoteFeed.send({ message: body, jsep: jsep });
              },
              error: function(error) {
                log(`创建订阅应答失败: ${error}`, 'error');
              }
            });
          }
        },
        onremotestream: function(stream) {
          log(`收到 ${display} 的远程流`);
          document.getElementById('remoteVideo').srcObject = stream;
          updateStatus(`正在显示 ${display} 的视频`, 'success');
        },
        oncleanup: function() {
          log(`清理 ${display} 的订阅连接`);
        }
      });
    }

    function leaveRoom() {
      if (videoroom) {
        const leave = { request: "leave" };
        videoroom.send({ message: leave });
        videoroom.hangup();
      }
      
      if (remoteFeed) {
        remoteFeed.hangup();
        remoteFeed = null;
      }
      
      if (localStream) {
        localStream.getTracks().forEach(track => track.stop());
        localStream = null;
      }
      
      document.getElementById('localVideo').srcObject = null;
      document.getElementById('remoteVideo').srcObject = null;
      
      updateStatus('已离开房间', 'info');
    }

    // 页面加载时的提示
    window.onload = function() {
      updateStatus('此版本专门修复单向连接问题，使用强制TURN中继模式', 'info');
      log('配置说明：');
      log('1. 强制使用TURN中继 (iceTransportPolicy: "relay")');
      log('2. 增加ICE连接超时时间');
      log('3. 添加自动重连机制');
      log('4. 优化订阅流程');
    };
  </script>
  
  <!-- 引入adapter.js和Janus库 -->
  <script src="adapter-latest.js"></script>
  <script src="janus.js"></script>
</body>
</html>
