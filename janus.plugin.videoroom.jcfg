# room-<unique room ID>: {
# description = This is my awesome room
# is_private = true|false (whether this room should be in the public list, default=true)
# secret = <optional password needed for manipulating (e.g. destroying) the room>
# pin = <optional password needed for joining the room>
# require_pvtid = true|false (whether subscriptions are required to provide a valid private_id
#			to associate with a publisher, default=false)
# signed_tokens = true|false (whether access to the room requires signed tokens; default=false,
#			only works if signed tokens are used in the core as well)
# publishers = <max number of concurrent senders> (e.g., 6 for a video
#              conference or 1 for a webinar)
# bitrate = <max video bitrate for senders> (e.g., 128000)
# bitrate_cap = true|false (whether the above cap should act as a hard limit to
#			dynamic bitrate changes by publishers; default=false, publishers can go beyond that)
# fir_freq = <send a FIR to publishers every fir_freq seconds> (0=disable)
# audiocodec = opus|g722|pcmu|pcma|isac32|isac16 (audio codec(s) to force on publishers, default=opus
#			can be a comma separated list in order of preference, e.g., opus,pcmu)
# videocodec = vp8|vp9|h264|av1|h265 (video codec(s) to force on publishers, default=vp8
#			can be a comma separated list in order of preference, e.g., vp9,vp8,h264)
# vp9_profile = VP9-specific profile to prefer (e.g., "2" for "profile-id=2")
# h264_profile = H.264-specific profile to prefer (e.g., "42e01f" for "profile-level-id=42e01f")
# opus_fec = true|false (whether inband FEC must be negotiated; only works for Opus, default=true)
# opus_dtx = true|false (whether DTX must be negotiated; only works for Opus, default=false)
# audiolevel_ext = true|false (whether the ssrc-audio-level RTP extension must
#		be negotiated/used or not for new publishers, default=true)
# audiolevel_event = true|false (whether to emit event to other users or not, default=false)
# audio_active_packets = 100 (number of packets with audio level, default=100, 2 seconds)
# audio_level_average = 25 (average value of audio level, 127=muted, 0='too loud', default=25)
# videoorient_ext = true|false (whether the video-orientation RTP extension must
#		be negotiated/used or not for new publishers, default=true)
# playoutdelay_ext = true|false (whether the playout-delay RTP extension must
#		be negotiated/used or not for new publishers, default=true)
# transport_wide_cc_ext = true|false (whether the transport wide CC RTP extension must be
#		negotiated/used or not for new publishers, default=true)
# record = true|false (whether this room should be recorded, default=false)
# rec_dir = <folder where recordings should be stored, when enabled>
# lock_record = true|false (whether recording can only be started/stopped if the secret
#            is provided, or using the global enable_recording request, default=false)
# notify_joining = true|false (optional, whether to notify all participants when a new
#               participant joins the room. The Videoroom plugin by design only notifies
#               new feeds (publishers), and enabling this may result extra notification
#               traffic. This flag is particularly useful when enabled with require_pvtid
#               for admin to manage listening only participants. default=false)
# require_e2ee = true|false (whether all participants are required to publish and subscribe
#             using end-to-end media encryption, e.g., via Insertable Streams; default=false)
# dummy_publisher = true|false (whether a dummy publisher should be created in this room,
#                 with one separate m-line for each codec supported in the room; this is
#                 useful when there's a need to create subscriptions with placeholders
#                 for some or all m-lines, even when they aren't used yet; default=false)
# dummy_streams = in case dummy_publisher is set to true, array of codecs to offer,
#				optionally with a fmtp attribute to match (codec/fmtp properties).
#				If not provided, all codecs enabled in the room are offered, with no fmtp.
#				Notice that the fmtp is parsed, and only a few codecs are supported.
# threads = number of threads to assist with the relaying of publishers in the room; as
#			in the Streaming plugin, this setting can help if you expect a lot of subscribers
#			that may cause the plugin to slow down and fail to catch up (default=0)
#}

general: {
	#admin_key = "supersecret"		# If set, rooms can be created via API only
									# if this key is provided in the request
	#lock_rtp_forward = true		# Whether the admin_key above should be
									# enforced for RTP forwarding requests too
	#events = false					# Whether events should be sent to event
									# handlers (default=true)

	# By default, integers are used as a unique ID for both rooms and participants.
	# In case you want to use strings instead (e.g., a UUID), set string_ids to true.
	#string_ids = true
}

room-1234: {
	description = "Demo Room"
	secret = "adminpwd"
	publishers = 6
	bitrate = 2000000	# 增加到 2 Mbps 支持高质量视频
	fir_freq = 10
	audiocodec = "opus"
	videocodec = "h264"  # 改为 H.264，兼容性更好
	record = false
	#rec_dir = "/path/to/recordings-folder"
}



room-1000: {
        description = "测试房间 - 自动创建"
        #secret = "adminpwd"
        publishers = 10
        bitrate = 2000000        # 增加到 2 Mbps 支持高质量视频
        bitrate_cap = false      # 允许动态调整比特率
        fir_freq = 10
        audiocodec = "opus"
        videocodec = "h264"  # 改为 H.264，兼容性更好
        record = false
        #rec_dir = "/path/to/recordings-folder"
}
