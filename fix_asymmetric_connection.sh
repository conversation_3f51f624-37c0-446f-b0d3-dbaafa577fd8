#!/bin/bash

# 修复单向连接问题的服务器配置脚本
# 基于Janus Gateway和COTURN项目的最佳实践

echo "=== 修复WebRTC单向连接问题 ==="
echo "基于Janus Gateway和COTURN项目的官方建议"
echo ""

# 检查是否为root用户
if [ "$EUID" -ne 0 ]; then
    echo "请使用root权限运行此脚本"
    exit 1
fi

# 停止现有服务
echo "1. 停止现有服务..."
pkill -f janus
pkill -f turnserver
sleep 3

# 检查Janus配置
echo "2. 验证Janus配置..."
if [ ! -f "janus.jcfg" ]; then
    echo "错误: 找不到janus.jcfg文件"
    exit 1
fi

# 验证配置文件语法
echo "验证配置文件语法..."
/opt/janus/bin/janus -C janus.jcfg -c 2>/dev/null
if [ $? -ne 0 ]; then
    echo "警告: Janus配置文件可能有语法错误"
fi

# 检查TURN配置
echo "3. 验证TURN配置..."
if [ ! -f "turnserver.conf" ]; then
    echo "错误: 找不到turnserver.conf文件"
    exit 1
fi

# 确保防火墙配置正确
echo "4. 配置防火墙..."
systemctl start firewalld

# Janus端口
firewall-cmd --permanent --add-port=8188/tcp
firewall-cmd --permanent --add-port=8088/tcp

# TURN端口
firewall-cmd --permanent --add-port=3478/tcp
firewall-cmd --permanent --add-port=3478/udp
firewall-cmd --permanent --add-port=3479/tcp
firewall-cmd --permanent --add-port=3479/udp

# RTP端口范围
firewall-cmd --permanent --add-port=20000-40000/udp

# TURN relay端口范围
firewall-cmd --permanent --add-port=49152-65535/udp

firewall-cmd --reload

echo "防火墙配置完成"

# 启动TURN服务器
echo "5. 启动TURN服务器..."
turnserver -c turnserver.conf -v &
TURN_PID=$!
sleep 2

# 检查TURN是否启动成功
if ps -p $TURN_PID > /dev/null; then
    echo "✓ TURN服务器启动成功 (PID: $TURN_PID)"
else
    echo "✗ TURN服务器启动失败"
    exit 1
fi

# 测试TURN服务器
echo "测试TURN服务器连通性..."
timeout 5 bash -c "echo > /dev/tcp/127.0.0.1/3478" 2>/dev/null
if [ $? -eq 0 ]; then
    echo "✓ TURN服务器端口3478可访问"
else
    echo "✗ TURN服务器端口3478不可访问"
fi

# 启动Janus
echo "6. 启动Janus服务器..."
cd /opt/janus
nohup bin/janus -C /root/janus/janus.jcfg > /var/log/janus.log 2>&1 &
JANUS_PID=$!
sleep 5

# 检查Janus是否启动成功
if ps -p $JANUS_PID > /dev/null; then
    echo "✓ Janus服务器启动成功 (PID: $JANUS_PID)"
else
    echo "✗ Janus服务器启动失败"
    echo "检查日志: tail -f /var/log/janus.log"
    exit 1
fi

# 测试Janus WebSocket
echo "测试Janus WebSocket连通性..."
timeout 5 bash -c "echo > /dev/tcp/127.0.0.1/8188" 2>/dev/null
if [ $? -eq 0 ]; then
    echo "✓ Janus WebSocket端口8188可访问"
else
    echo "✗ Janus WebSocket端口8188不可访问"
fi

# 显示服务状态
echo ""
echo "=== 服务状态 ==="
echo "Janus PID: $JANUS_PID"
echo "TURN PID: $TURN_PID"

echo ""
echo "=== 端口监听状态 ==="
netstat -tlnp | grep -E "(8188|3478|3479)" | head -10

echo ""
echo "=== 针对单向连接问题的配置说明 ==="
echo "1. 修改了Janus配置："
echo "   - full_trickle = false (禁用完全trickle ICE)"
echo "   - ice_nomination = regular (使用常规ICE提名)"
echo "   - ice_consent_freshness = true (启用连接保活)"
echo "   - dtls_timeout = 1000 (增加DTLS超时)"
echo "   - no_media_timer = 10 (增加媒体超时)"
echo ""
echo "2. 客户端建议："
echo "   - 使用 videoroom_asymmetric_fix.html"
echo "   - 强制TURN中继模式"
echo "   - 自动重连机制"
echo ""

# 创建测试脚本
cat > test_connection.py << 'EOF'
#!/usr/bin/env python3
import socket
import sys

def test_port(host, port, protocol='tcp'):
    try:
        if protocol == 'tcp':
            sock = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
            sock.settimeout(5)
            result = sock.connect_ex((host, port))
            sock.close()
            return result == 0
        else:
            sock = socket.socket(socket.AF_INET, socket.SOCK_DGRAM)
            sock.settimeout(5)
            sock.sendto(b'test', (host, port))
            sock.close()
            return True
    except:
        return False

print("=== 连接测试 ===")
tests = [
    ('127.0.0.1', 8188, 'tcp', 'Janus WebSocket'),
    ('127.0.0.1', 3478, 'tcp', 'TURN TCP'),
    ('127.0.0.1', 3478, 'udp', 'TURN UDP'),
    ('*************', 8188, 'tcp', 'Janus外部访问'),
    ('*************', 3478, 'tcp', 'TURN外部TCP'),
]

for host, port, protocol, name in tests:
    result = test_port(host, port, protocol)
    status = "✓" if result else "✗"
    print(f"{status} {name}: {host}:{port}/{protocol}")
EOF

chmod +x test_connection.py
python3 test_connection.py

echo ""
echo "=== 完成 ==="
echo "服务已启动，请使用以下文件进行测试："
echo "1. videoroom_asymmetric_fix.html (专门修复单向连接)"
echo "2. 如果问题仍然存在，请检查客户端网络环境"
echo ""
echo "日志文件："
echo "- Janus: /var/log/janus.log"
echo "- TURN: /var/log/turnserver.log"
echo ""
echo "监控命令："
echo "- tail -f /var/log/janus.log"
echo "- ./test_connection.py"
