<!DOCTYPE html>
<html>
<head>
  <title>Firefox WebRTC 修复测试</title>
  <style>
    body { font-family: Arial, sans-serif; margin: 20px; }
    video { width: 320px; height: 240px; border: 2px solid #000; margin: 10px; }
    button { padding: 10px 20px; margin: 5px; font-size: 16px; }
    #log { height: 300px; overflow-y: scroll; border: 1px solid #ccc; padding: 10px; font-family: monospace; font-size: 12px; }
    .status { padding: 10px; margin: 10px 0; border-radius: 5px; }
    .success { background-color: #d4edda; color: #155724; }
    .error { background-color: #f8d7da; color: #721c24; }
    .warning { background-color: #fff3cd; color: #856404; }
  </style>
</head>
<body>
  <h1>Firefox WebRTC 修复测试</h1>
  <p><strong>专门针对 Firefox 浏览器的 WebRTC 连接问题</strong></p>
  
  <div id="status" class="status">准备测试...</div>
  
  <div>
    <button onclick="startFirefoxTest()">开始 Firefox 测试</button>
    <button onclick="clearLog()">清除日志</button>
  </div>
  
  <div>
    <h3>本地视频</h3>
    <video id="localVideo" autoplay muted playsinline></video>
    
    <h3>远程视频</h3>
    <video id="remoteVideo" autoplay playsinline></video>
  </div>
  
  <div>
    <h3>调试日志</h3>
    <div id="log"></div>
  </div>

  <script src="adapter-latest.js"></script>
  <script src="janus.js"></script>
  
  <script>
    let janus = null;
    let videoroom = null;
    let localStream = null;
    
    function log(message) {
      const timestamp = new Date().toLocaleTimeString();
      const logDiv = document.getElementById('log');
      logDiv.innerHTML += `[${timestamp}] ${message}<br>`;
      logDiv.scrollTop = logDiv.scrollHeight;
      console.log(`[${timestamp}] ${message}`);
    }
    
    function updateStatus(message, type = 'info') {
      const statusDiv = document.getElementById('status');
      statusDiv.textContent = message;
      statusDiv.className = `status ${type}`;
      log(`状态: ${message}`);
    }
    
    function clearLog() {
      document.getElementById('log').innerHTML = '';
    }
    
    async function startFirefoxTest() {
      log('🦊 开始 Firefox 专用测试');
      log('🔍 浏览器信息: ' + navigator.userAgent);
      
      // 检查是否为 Firefox
      if (!navigator.userAgent.includes('Firefox')) {
        updateStatus('警告：这不是 Firefox 浏览器', 'warning');
      }
      
      try {
        // 获取摄像头
        log('📹 获取摄像头...');
        localStream = await navigator.mediaDevices.getUserMedia({
          video: { width: 640, height: 480 },
          audio: false
        });
        
        document.getElementById('localVideo').srcObject = localStream;
        log('✅ 摄像头获取成功');
        
        // 初始化 Janus
        log('🔧 初始化 Janus...');
        Janus.init({
          debug: "all",
          callback: function() {
            log('✅ Janus 初始化成功');
            connectToJanusFirefox();
          }
        });
        
      } catch (error) {
        log('❌ 测试失败: ' + error.message);
        updateStatus('测试失败: ' + error.message, 'error');
      }
    }
    
    function connectToJanusFirefox() {
      // Firefox 专用配置
      const firefoxConfig = {
        server: "ws://60.255.197.32:8188/",
        // Firefox 特殊的 ICE 配置
        iceServers: [
          { urls: "stun:stun.l.google.com:19302" }
        ],
        // Firefox 兼容性设置
        iceTransportPolicy: "all",
        bundlePolicy: "max-bundle",
        rtcpMuxPolicy: "require",
        iceCandidatePoolSize: 0,  // Firefox 有时对这个敏感
        success: function() {
          log('✅ Janus 连接成功');
          updateStatus('Janus 连接成功', 'success');
          attachVideoRoomFirefox();
        },
        error: function(error) {
          log('❌ Janus 连接失败: ' + error);
          updateStatus('Janus 连接失败: ' + error, 'error');
        },
        destroyed: function() {
          log('⚠️ Janus 连接已断开');
        }
      };
      
      janus = new Janus(firefoxConfig);
    }
    
    function attachVideoRoomFirefox() {
      janus.attach({
        plugin: "janus.plugin.videoroom",
        opaqueId: "firefox-test-" + Janus.randomString(12),
        success: function(pluginHandle) {
          videoroom = pluginHandle;
          log('✅ VideoRoom 插件连接成功');
          
          // 加入房间
          const register = {
            request: "join",
            room: 1000,
            ptype: "publisher",
            display: "Firefox-192.168.1.2",
            pin: ""
          };
          
          videoroom.send({ message: register });
          log('📤 发送加入房间请求');
        },
        error: function(error) {
          log('❌ VideoRoom 连接失败: ' + error);
          updateStatus('VideoRoom 连接失败: ' + error, 'error');
        },
        iceState: function(state) {
          log(`🧊 ICE 状态: ${state}`);
          if (state === 'connected') {
            updateStatus('ICE 连接成功', 'success');
          } else if (state === 'failed') {
            updateStatus('ICE 连接失败', 'error');
            // Firefox 重连机制
            setTimeout(() => {
              log('🔄 Firefox 重连尝试...');
              if (videoroom) {
                videoroom.hangup();
                setTimeout(attachVideoRoomFirefox, 2000);
              }
            }, 1000);
          }
        },
        mediaState: function(medium, on) {
          log(`📺 媒体状态: ${medium} ${on ? '开启' : '关闭'}`);
        },
        webrtcState: function(on) {
          log(`🔗 WebRTC 状态: ${on ? '已连接' : '已断开'}`);
          if (on) {
            updateStatus('WebRTC 连接成功', 'success');
          } else {
            updateStatus('WebRTC 连接断开', 'error');
            // Firefox 特殊处理：WebRTC 断开时不立即重连，等待一段时间
            log('⏳ Firefox WebRTC 断开，等待重新连接...');
          }
        },
        onmessage: function(msg, jsep) {
          log('📨 收到消息: ' + JSON.stringify(msg));
          
          if (msg["videoroom"] === "joined") {
            log('✅ 已加入房间');
            updateStatus('已加入房间，开始发布流...', 'success');
            
            // Firefox 专用的 offer 创建
            const offerOptions = {
              media: {
                audioRecv: false,
                videoRecv: false,
                audioSend: false,
                videoSend: true,
                data: false
              },
              stream: localStream,
              // Firefox 特殊选项
              trickle: true,
              success: function(jsep) {
                log('✅ 创建 offer 成功');
                const publish = {
                  request: "configure",
                  audio: false,
                  video: true,
                  bitrate: 500000  // 降低比特率，Firefox 有时对高比特率敏感
                };
                videoroom.send({ message: publish, jsep: jsep });
              },
              error: function(error) {
                log('❌ 创建 offer 失败: ' + error);
                updateStatus('创建 offer 失败: ' + error, 'error');
              }
            };
            
            videoroom.createOffer(offerOptions);
            
            // 检查是否有其他发布者
            if (msg["publishers"]) {
              msg["publishers"].forEach(publisher => {
                if (publisher["id"] !== msg["id"]) {
                  log(`🔍 发现其他发布者: ${publisher["display"]} (${publisher["id"]})`);
                  setTimeout(() => {
                    subscribeToFeedFirefox(publisher["id"], publisher["display"]);
                  }, 2000);  // Firefox 需要延迟订阅
                }
              });
            }
          } else if (msg["videoroom"] === "event") {
            if (msg["configured"] === "ok") {
              log('✅ 发布配置成功');
              updateStatus('本地流发布成功', 'success');
            }
            
            if (msg["publishers"]) {
              msg["publishers"].forEach(publisher => {
                log(`🔍 新发布者: ${publisher["display"]} (${publisher["id"]})`);
                setTimeout(() => {
                  subscribeToFeedFirefox(publisher["id"], publisher["display"]);
                }, 2000);  // Firefox 延迟订阅
              });
            }
          }
          
          if (jsep) {
            log('📥 处理 SDP: ' + jsep.type);
            videoroom.handleRemoteJsep({ jsep: jsep });
          }
        }
      });
    }
    
    function subscribeToFeedFirefox(id, display) {
      log(`🔄 Firefox 订阅 ${display} (ID: ${id})`);
      
      janus.attach({
        plugin: "janus.plugin.videoroom",
        opaqueId: "firefox-subscriber-" + Janus.randomString(12),
        success: function(pluginHandle) {
          log('✅ Firefox 订阅插件连接成功');
          
          const subscribe = {
            request: "join",
            room: 1000,
            ptype: "subscriber",
            feed: id,
            private_id: Math.floor(Math.random() * 1000000),
            offer_audio: false,
            offer_video: true,
            pin: ""
          };
          
          pluginHandle.send({ message: subscribe });
        },
        error: function(error) {
          log('❌ Firefox 订阅失败: ' + error);
        },
        iceState: function(state) {
          log(`🧊 Firefox 订阅 ICE 状态: ${state}`);
        },
        webrtcState: function(on) {
          log(`🔗 Firefox 订阅 WebRTC 状态: ${on ? '已连接' : '已断开'}`);
          if (on) {
            updateStatus('Firefox 订阅连接成功！', 'success');
          }
        },
        onremotetrack: function(track, mid, on) {
          log(`🎬 Firefox 收到远程轨道: ${track.kind}, mid: ${mid}, on: ${on}`);
          
          if (track.kind === 'video' && on) {
            const remoteVideo = document.getElementById('remoteVideo');
            const stream = new MediaStream([track]);
            remoteVideo.srcObject = stream;
            
            remoteVideo.onloadedmetadata = function() {
              log('✅ Firefox 远程视频元数据加载完成');
              remoteVideo.play().then(() => {
                log('✅ Firefox 远程视频开始播放');
                updateStatus('Firefox 视频播放成功！', 'success');
              }).catch(e => {
                log('❌ Firefox 远程视频播放失败: ' + e.message);
              });
            };
          }
        },
        onmessage: function(msg, jsep) {
          log('📨 Firefox 订阅消息: ' + JSON.stringify(msg));
          
          if (jsep) {
            log('📥 Firefox 处理订阅 SDP: ' + jsep.type);
            pluginHandle.createAnswer({
              jsep: jsep,
              media: { audioSend: false, videoSend: false },
              success: function(jsep) {
                log('✅ Firefox 订阅应答创建成功');
                const body = { request: "start", room: 1000 };
                pluginHandle.send({ message: body, jsep: jsep });
              },
              error: function(error) {
                log('❌ Firefox 订阅应答创建失败: ' + error);
              }
            });
          }
        }
      });
    }
  </script>
</body>
</html>
