#!/bin/bash

echo "=== 检查客户端网络连通性 ==="
echo "时间: $(date)"
echo ""

CLIENT_IP="***********"
SERVER_IP="*************"

# 1. 基础网络测试
echo "1. 基础网络连通性测试"
echo "===================="

echo "从服务器 ping 客户端:"
if ping -c 3 -W 2 $CLIENT_IP; then
    echo "✅ 可以 ping 通客户端"
else
    echo "❌ 无法 ping 通客户端"
fi

echo ""
echo "检查路由到客户端:"
ip route get $CLIENT_IP

echo ""
echo "检查 ARP 表中的客户端:"
arp -a | grep $CLIENT_IP || echo "客户端不在 ARP 表中"

echo ""
echo "2. 监听所有来自客户端的流量"
echo "=========================="

echo "监听任何来自 $CLIENT_IP 的包 (30秒)..."
echo "请在客户端开始 WebRTC 测试"

timeout 30 tcpdump -i any -n "host $CLIENT_IP" 2>/dev/null | head -20

echo ""
echo "3. 检查防火墙和网络配置"
echo "======================"

echo "检查 iptables 规则:"
iptables -L INPUT -n | grep -E "$CLIENT_IP|192.168" || echo "没有针对客户端的特殊规则"

echo ""
echo "检查网络接口:"
ip addr show | grep -E "inet.*192\.168|inet.*10\.|inet.*172\."

echo ""
echo "检查网络命名空间:"
ip netns list 2>/dev/null || echo "没有网络命名空间"

echo ""
echo "4. 测试特定端口连通性"
echo "===================="

echo "测试客户端是否能连接到服务器端口:"
PORTS=(8188 8088 3478)

for port in "${PORTS[@]}"; do
    echo "测试端口 $port:"
    if netstat -tlnp | grep ":$port " >/dev/null; then
        echo "  ✅ 端口 $port 正在监听"
        
        # 尝试从客户端方向测试
        echo "  等待来自客户端的连接..."
        timeout 5 nc -l $port &
        NC_PID=$!
        sleep 1
        kill $NC_PID 2>/dev/null
    else
        echo "  ❌ 端口 $port 未监听"
    fi
done

echo ""
echo "5. 网络诊断建议"
echo "================"

echo "基于测试结果，可能的问题:"
echo ""

if ! ping -c 1 -W 2 $CLIENT_IP >/dev/null 2>&1; then
    echo "❌ 网络层连通性问题:"
    echo "   - 客户端和服务器不在同一网络段"
    echo "   - 中间路由器/防火墙阻止了流量"
    echo "   - 客户端防火墙阻止了出站流量"
    echo "   - NAT 配置问题"
    echo ""
    echo "建议解决方案:"
    echo "   1. 检查客户端网络配置"
    echo "   2. 检查中间网络设备"
    echo "   3. 使用公网 STUN/TURN 服务器"
    echo "   4. 配置端口映射"
else
    echo "✅ 基础连通性正常，问题可能在应用层"
fi

echo ""
echo "6. 客户端测试建议"
echo "================"

echo "请在客户端 ($CLIENT_IP) 执行以下测试:"
echo ""
echo "A. 基础连通性测试:"
echo "   ping $SERVER_IP"
echo "   telnet $SERVER_IP 8188"
echo "   curl http://$SERVER_IP:8088/janus/info"
echo ""
echo "B. 防火墙测试:"
echo "   # Windows"
echo "   netsh advfirewall show allprofiles"
echo "   # 临时关闭防火墙测试"
echo ""
echo "C. 网络路由测试:"
echo "   tracert $SERVER_IP  # Windows"
echo "   route print         # Windows"
echo ""
echo "D. 浏览器网络测试:"
echo "   # Chrome: chrome://net-internals/"
echo "   # Firefox: about:networking"

echo ""
echo "=== 检查完成 ==="
