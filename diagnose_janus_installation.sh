#!/bin/bash

echo "=== Janus 安装和配置全面诊断 ==="
echo "时间: $(date)"
echo ""

# 1. 检查 Janus 版本和编译信息
echo "1. Janus 版本和编译信息"
echo "======================"
if [ -f "/opt/janus/bin/janus" ]; then
    echo "✅ Janus 可执行文件存在"
    /opt/janus/bin/janus --version 2>/dev/null || echo "⚠️  无法获取版本信息"
    echo ""
    echo "编译依赖检查:"
    ldd /opt/janus/bin/janus | grep -E "ssl|crypto|srtp|nice|websockets"
else
    echo "❌ Janus 可执行文件不存在"
    exit 1
fi
echo ""

# 2. 检查关键库文件
echo "2. 关键库文件检查"
echo "================"
LIBS=(
    "libssl"
    "libcrypto" 
    "libsrtp2"
    "libnice"
    "libwebsockets"
)

for lib in "${LIBS[@]}"; do
    if ldconfig -p | grep -q "$lib"; then
        echo "✅ $lib 已安装"
    else
        echo "❌ $lib 未找到"
    fi
done
echo ""

# 3. 检查 Janus 插件
echo "3. Janus 插件检查"
echo "================"
PLUGINS_DIR="/opt/janus/lib/janus/plugins"
if [ -d "$PLUGINS_DIR" ]; then
    echo "VideoRoom 插件:"
    if [ -f "$PLUGINS_DIR/libjanus_videoroom.so" ]; then
        echo "✅ VideoRoom 插件存在"
        ldd "$PLUGINS_DIR/libjanus_videoroom.so" | grep -E "ssl|crypto|srtp" | head -3
    else
        echo "❌ VideoRoom 插件不存在"
    fi
else
    echo "❌ 插件目录不存在"
fi
echo ""

# 4. 检查传输插件
echo "4. 传输插件检查"
echo "================"
TRANSPORTS_DIR="/opt/janus/lib/janus/transports"
if [ -d "$TRANSPORTS_DIR" ]; then
    echo "WebSocket 传输:"
    if [ -f "$TRANSPORTS_DIR/libjanus_websockets.so" ]; then
        echo "✅ WebSocket 传输存在"
        ldd "$TRANSPORTS_DIR/libjanus_websockets.so" | grep websockets
    else
        echo "❌ WebSocket 传输不存在"
    fi
else
    echo "❌ 传输目录不存在"
fi
echo ""

# 5. 检查配置文件语法
echo "5. 配置文件语法检查"
echo "=================="
CONFIG_DIR="/opt/janus/etc/janus"
if [ -d "$CONFIG_DIR" ]; then
    echo "主配置文件:"
    if /opt/janus/bin/janus -C "$CONFIG_DIR/janus.jcfg" -L 2>&1 | grep -q "Configuration parsed"; then
        echo "✅ 主配置文件语法正确"
    else
        echo "❌ 主配置文件语法错误"
        /opt/janus/bin/janus -C "$CONFIG_DIR/janus.jcfg" -L 2>&1 | tail -5
    fi
else
    echo "❌ 配置目录不存在"
fi
echo ""

# 6. 检查网络配置
echo "6. 网络配置检查"
echo "================"
echo "本机 IP 地址:"
ip addr show | grep -E "inet.*192\.168|inet.*10\.|inet.*172\." | head -3

echo ""
echo "路由表:"
ip route | head -3

echo ""
echo "防火墙状态:"
if command -v ufw >/dev/null; then
    ufw status
elif command -v firewall-cmd >/dev/null; then
    firewall-cmd --list-all | head -10
else
    echo "未检测到防火墙管理工具"
fi
echo ""

# 7. 检查端口占用
echo "7. 端口占用检查"
echo "================"
PORTS=(8188 8088 20000 20001 20002)
for port in "${PORTS[@]}"; do
    if netstat -tlnp 2>/dev/null | grep ":$port " >/dev/null; then
        echo "✅ 端口 $port 正在监听"
    else
        echo "⚠️  端口 $port 未监听"
    fi
done
echo ""

# 8. 检查 SRTP 支持
echo "8. SRTP 支持检查"
echo "================"
if [ -f "/opt/janus/bin/janus" ]; then
    echo "检查 SRTP 库链接:"
    ldd /opt/janus/bin/janus | grep srtp
    
    echo ""
    echo "检查 OpenSSL 版本:"
    openssl version
    
    echo ""
    echo "检查 libsrtp 版本:"
    if pkg-config --exists libsrtp2; then
        echo "✅ libsrtp2: $(pkg-config --modversion libsrtp2)"
    elif pkg-config --exists libsrtp; then
        echo "✅ libsrtp: $(pkg-config --modversion libsrtp)"
    else
        echo "⚠️  无法检测 libsrtp 版本"
    fi
fi
echo ""

# 9. 测试简单启动
echo "9. 测试 Janus 启动"
echo "=================="
echo "尝试启动 Janus (测试模式)..."
cd /opt/janus
timeout 10 bin/janus -C etc/janus/janus.jcfg -L 2>&1 | head -20

echo ""
echo "=== 诊断完成 ==="
echo ""
echo "如果发现问题，可能需要:"
echo "1. 重新编译 Janus (如果依赖库有问题)"
echo "2. 检查 libsrtp 版本兼容性"
echo "3. 检查 OpenSSL 版本"
echo "4. 检查网络配置和防火墙"
