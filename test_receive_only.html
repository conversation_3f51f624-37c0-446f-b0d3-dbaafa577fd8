<!DOCTYPE html>
<html>
<head>
  <title>192.168.1.2 接收测试</title>
  <style>
    body { font-family: Arial, sans-serif; margin: 20px; }
    video { width: 320px; height: 240px; border: 2px solid #000; margin: 10px; }
    button { padding: 10px 20px; margin: 5px; font-size: 16px; }
    #log { height: 300px; overflow-y: scroll; border: 1px solid #ccc; padding: 10px; font-family: monospace; font-size: 12px; }
    .status { padding: 10px; margin: 10px 0; border-radius: 5px; }
    .success { background-color: #d4edda; color: #155724; }
    .error { background-color: #f8d7da; color: #721c24; }
  </style>
</head>
<body>
  <h1>192.168.1.2 专用接收测试</h1>
  <p><strong>目标：只测试视频接收功能，不发送视频</strong></p>
  
  <div id="status" class="status">准备测试...</div>
  
  <div>
    <button onclick="startReceiveTest()">开始接收测试</button>
    <button onclick="clearLog()">清除日志</button>
  </div>
  
  <div>
    <h3>接收到的视频</h3>
    <video id="remoteVideo" autoplay playsinline controls></video>
  </div>
  
  <div>
    <h3>调试日志</h3>
    <div id="log"></div>
  </div>

  <script src="adapter-latest.js"></script>
  <script src="janus.js"></script>
  
  <script>
    let janus = null;
    let remoteFeed = null;
    
    function log(message) {
      const timestamp = new Date().toLocaleTimeString();
      const logDiv = document.getElementById('log');
      logDiv.innerHTML += `[${timestamp}] ${message}<br>`;
      logDiv.scrollTop = logDiv.scrollHeight;
      console.log(`[${timestamp}] ${message}`);
    }
    
    function updateStatus(message, type = 'info') {
      const statusDiv = document.getElementById('status');
      statusDiv.textContent = message;
      statusDiv.className = `status ${type}`;
      log(`状态: ${message}`);
    }
    
    function clearLog() {
      document.getElementById('log').innerHTML = '';
    }
    
    function startReceiveTest() {
      log('🔍 开始接收测试 - 只订阅，不发布');
      updateStatus('开始接收测试...', 'info');
      
      Janus.init({
        debug: "all",
        callback: function() {
          log('✅ Janus 初始化成功');
          connectToJanus();
        }
      });
    }
    
    function connectToJanus() {
      janus = new Janus({
        server: "ws://60.255.197.32:8188/",
        iceServers: [
          { urls: "stun:stun.l.google.com:19302" },
          { urls: "turn:60.255.197.32:3478", username: "hhzt", credential: "hhzt20130403" }
        ],
        success: function() {
          log('✅ Janus 连接成功');
          updateStatus('Janus 连接成功', 'success');
          
          // 直接查询房间中的发布者
          queryRoomPublishers();
        },
        error: function(error) {
          log('❌ Janus 连接失败: ' + error);
          updateStatus('Janus 连接失败: ' + error, 'error');
        }
      });
    }
    
    function queryRoomPublishers() {
      janus.attach({
        plugin: "janus.plugin.videoroom",
        success: function(pluginHandle) {
          log('✅ VideoRoom 插件连接成功');
          
          // 查询房间中的发布者
          const listRequest = {
            request: "listparticipants",
            room: 1000
          };
          
          pluginHandle.send({
            message: listRequest,
            success: function(result) {
              log('📋 房间参与者列表: ' + JSON.stringify(result));
              
              if (result.participants && result.participants.length > 0) {
                // 找到第一个发布者并订阅
                const publisher = result.participants[0];
                log(`🎯 找到发布者: ${publisher.display} (ID: ${publisher.id})`);
                subscribeToPublisher(publisher.id, publisher.display);
              } else {
                log('⚠️ 房间中没有发布者');
                updateStatus('房间中没有发布者，请先在其他设备上发布视频', 'error');
              }
            },
            error: function(error) {
              log('❌ 查询参与者失败: ' + error);
            }
          });
        },
        error: function(error) {
          log('❌ VideoRoom 连接失败: ' + error);
        }
      });
    }
    
    function subscribeToPublisher(publisherId, publisherName) {
      log(`🔄 开始订阅 ${publisherName} (ID: ${publisherId})`);
      
      janus.attach({
        plugin: "janus.plugin.videoroom",
        success: function(pluginHandle) {
          remoteFeed = pluginHandle;
          log('✅ 订阅插件连接成功');
          
          const subscribe = {
            request: "join",
            room: 1000,
            ptype: "subscriber",
            feed: publisherId,
            private_id: Math.floor(Math.random() * 1000000),
            offer_audio: false,
            offer_video: true,
            pin: ""
          };
          
          remoteFeed.send({ message: subscribe });
          log('📤 发送订阅请求');
        },
        error: function(error) {
          log('❌ 订阅插件连接失败: ' + error);
          updateStatus('订阅失败: ' + error, 'error');
        },
        iceState: function(state) {
          log(`🧊 订阅 ICE 状态: ${state}`);
          if (state === 'connected') {
            updateStatus('ICE 连接成功', 'success');
          } else if (state === 'failed') {
            updateStatus('ICE 连接失败', 'error');
          }
        },
        webrtcState: function(on) {
          log(`🔗 订阅 WebRTC 状态: ${on ? '已连接' : '已断开'}`);
          if (on) {
            updateStatus('WebRTC 连接成功', 'success');
          } else {
            updateStatus('WebRTC 连接断开', 'error');
          }
        },
        onremotetrack: function(track, mid, on) {
          log(`🎬 收到远程轨道: ${track.kind}, mid: ${mid}, on: ${on}`);
          log(`🔍 轨道详情: enabled=${track.enabled}, muted=${track.muted}, readyState=${track.readyState}`);
          
          if (track.kind === 'video' && on) {
            const remoteVideo = document.getElementById('remoteVideo');
            
            // 创建新的媒体流
            const stream = new MediaStream([track]);
            remoteVideo.srcObject = stream;
            
            log('📺 设置远程视频流');
            
            // 监听视频事件
            remoteVideo.onloadedmetadata = function() {
              log(`✅ 视频元数据加载: ${remoteVideo.videoWidth}x${remoteVideo.videoHeight}`);
            };
            
            remoteVideo.onplay = function() {
              log('▶️ 视频开始播放');
              updateStatus('视频播放成功！', 'success');
            };
            
            remoteVideo.onerror = function(e) {
              log('❌ 视频播放错误: ' + e.message);
              updateStatus('视频播放失败', 'error');
            };
            
            // 强制播放
            remoteVideo.play().then(() => {
              log('✅ 手动播放成功');
            }).catch(e => {
              log('⚠️ 手动播放失败: ' + e.message);
            });
          }
        },
        onmessage: function(msg, jsep) {
          log('📨 订阅消息: ' + JSON.stringify(msg));
          
          if (jsep) {
            log('📥 处理 SDP: ' + jsep.type);
            
            remoteFeed.createAnswer({
              jsep: jsep,
              media: { audioSend: false, videoSend: false },
              success: function(jsep) {
                log('✅ 创建应答成功');
                const body = { request: "start", room: 1000 };
                remoteFeed.send({ message: body, jsep: jsep });
              },
              error: function(error) {
                log('❌ 创建应答失败: ' + error);
                updateStatus('SDP 应答失败: ' + error, 'error');
              }
            });
          }
        }
      });
    }
  </script>
</body>
</html>
