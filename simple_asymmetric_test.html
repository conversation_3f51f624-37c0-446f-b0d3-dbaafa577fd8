<!DOCTYPE html>
<html>
<head>
  <meta charset="UTF-8">
  <title>简单单向连接测试</title>
  <style>
    body { font-family: Arial, sans-serif; margin: 20px; }
    .video-container { display: flex; gap: 20px; margin: 20px 0; }
    video { width: 320px; height: 240px; border: 1px solid #ccc; }
    .controls { margin: 20px 0; }
    button { padding: 10px 20px; margin: 5px; }
    .status { margin: 10px 0; padding: 10px; border-radius: 3px; }
    .success { background-color: #d4edda; color: #155724; }
    .error { background-color: #f8d7da; color: #721c24; }
    .warning { background-color: #fff3cd; color: #856404; }
    .info { background-color: #d1ecf1; color: #0c5460; }
    #log { height: 300px; overflow-y: auto; border: 1px solid #ccc; padding: 10px; background: #f9f9f9; font-family: monospace; font-size: 12px; }
  </style>
</head>
<body>
  <h1>简单单向连接测试</h1>
  
  <div class="controls">
    <button onclick="checkJanus()">检查Janus库</button>
    <button onclick="startTest()">开始测试</button>
    <button onclick="clearLog()">清空日志</button>
  </div>

  <div id="status" class="status info">准备就绪</div>

  <div class="video-container">
    <div>
      <h3>本地视频</h3>
      <video id="localVideo" autoplay muted></video>
    </div>
    <div>
      <h3>远程视频</h3>
      <video id="remoteVideo" autoplay></video>
    </div>
  </div>

  <div>
    <h3>详细日志</h3>
    <div id="log"></div>
  </div>

  <!-- 先引入adapter.js，再引入Janus库 -->
  <script src="adapter-latest.js"></script>
  <script src="janus.js"></script>
  
  <script>
    let janus = null;
    let videoroom = null;
    let localStream = null;
    let myid = null;
    let myroom = 1234;

    function log(message, type = 'info') {
      const logDiv = document.getElementById('log');
      const timestamp = new Date().toLocaleTimeString();
      const className = type === 'error' ? 'error' : type === 'warning' ? 'warning' : type === 'success' ? 'success' : 'info';
      logDiv.innerHTML += `<div class="${className}">[${timestamp}] ${message}</div>`;
      logDiv.scrollTop = logDiv.scrollHeight;
      console.log(message);
    }

    function updateStatus(message, type = 'info') {
      const statusDiv = document.getElementById('status');
      statusDiv.className = `status ${type}`;
      statusDiv.textContent = message;
      log(message, type);
    }

    function clearLog() {
      document.getElementById('log').innerHTML = '';
    }

    function checkJanus() {
      if (typeof Janus === 'undefined') {
        updateStatus('Janus库未加载！请确保janus.js文件存在', 'error');
        log('请检查以下文件是否存在：');
        log('1. janus.js - Janus WebRTC库');
        log('2. 确保文件路径正确');
        log('3. 或者使用在线版本：https://janus.conf.meetecho.com/janus.js');
        return false;
      } else {
        updateStatus('Janus库加载成功！', 'success');
        log(`Janus版本信息: ${Janus.getVersion()}`);
        return true;
      }
    }

    async function startTest() {
      // 首先检查Janus库
      if (!checkJanus()) {
        return;
      }

      try {
        updateStatus('正在获取摄像头权限...', 'info');
        
        localStream = await navigator.mediaDevices.getUserMedia({
          video: { width: 320, height: 240 },
          audio: true
        });
        
        document.getElementById('localVideo').srcObject = localStream;
        updateStatus('摄像头已启动', 'success');
        
        // 初始化Janus
        initJanus();
        
      } catch (error) {
        updateStatus(`获取摄像头失败: ${error.message}`, 'error');
      }
    }

    function initJanus() {
      updateStatus('正在初始化Janus...', 'info');
      
      Janus.init({
        debug: "all",
        callback: function() {
          log('Janus初始化成功');
          
          // 强制TURN中继配置
          const iceServers = [
            { urls: "turn:60.255.197.32:3478", username: "hhzt", credential: "hhzt20130403" },
            { urls: "turn:60.255.197.32:3479", username: "hhzt", credential: "hhzt20130403" }
          ];

          janus = new Janus({
            server: "ws://60.255.197.32:8188/",
            iceServers: iceServers,
            iceTransportPolicy: "relay", // 强制使用TURN中继
            success: function() {
              updateStatus('Janus连接成功', 'success');
              attachVideoRoom();
            },
            error: function(error) {
              updateStatus(`Janus连接失败: ${error}`, 'error');
            },
            destroyed: function() {
              updateStatus('Janus连接已断开', 'warning');
            }
          });
        },
        error: function(error) {
          updateStatus(`Janus初始化失败: ${error}`, 'error');
        }
      });
    }

    function attachVideoRoom() {
      updateStatus('正在连接VideoRoom插件...', 'info');
      
      janus.attach({
        plugin: "janus.plugin.videoroom",
        success: function(pluginHandle) {
          videoroom = pluginHandle;
          updateStatus('VideoRoom插件连接成功', 'success');
          
          // 自动加入房间
          const register = {
            request: "join",
            room: myroom,
            ptype: "publisher",
            display: "测试用户_" + Janus.randomString(4)
          };
          
          videoroom.send({ message: register });
          log('发送加入房间请求');
        },
        error: function(error) {
          updateStatus(`VideoRoom插件连接失败: ${error}`, 'error');
        },
        iceState: function(state) {
          log(`ICE状态: ${state}`);
          if (state === 'connected') {
            updateStatus('ICE连接已建立', 'success');
          } else if (state === 'failed') {
            updateStatus('ICE连接失败', 'error');
          }
        },
        webrtcState: function(on) {
          log(`WebRTC状态: ${on ? '已连接' : '已断开'}`);
        },
        onmessage: function(msg, jsep) {
          log("收到消息: " + JSON.stringify(msg));
          
          const event = msg["videoroom"];
          if (event === "joined") {
            myid = msg["id"];
            updateStatus(`已加入房间，ID: ${myid}`, 'success');
            
            // 发布本地流
            publishOwnFeed();
            
            // 检查是否有其他发布者
            if (msg["publishers"]) {
              const list = msg["publishers"];
              for (let f in list) {
                const id = list[f]["id"];
                const display = list[f]["display"];
                log(`发现发布者: ${display} (${id})`);
                subscribeToFeed(id, display);
              }
            }
          } else if (event === "event") {
            if (msg["publishers"]) {
              const list = msg["publishers"];
              for (let f in list) {
                const id = list[f]["id"];
                const display = list[f]["display"];
                log(`新发布者加入: ${display} (${id})`);
                subscribeToFeed(id, display);
              }
            }
          }
          
          if (jsep) {
            log("处理SDP: " + jsep.type);
            videoroom.handleRemoteJsep({ jsep: jsep });
          }
        },
        onlocalstream: function(stream) {
          log("本地流已准备");
        },
        onremotestream: function(stream) {
          log("收到远程流");
          document.getElementById('remoteVideo').srcObject = stream;
          updateStatus('远程视频流已连接', 'success');
        },
        oncleanup: function() {
          log("清理WebRTC连接");
        }
      });
    }

    function publishOwnFeed() {
      log('开始发布本地流...');
      videoroom.createOffer({
        media: { audioRecv: false, videoRecv: false, audioSend: true, videoSend: true },
        stream: localStream,
        success: function(jsep) {
          log("发布本地流成功");
          const publish = { request: "configure", audio: true, video: true };
          videoroom.send({ message: publish, jsep: jsep });
        },
        error: function(error) {
          updateStatus(`发布失败: ${error}`, 'error');
        }
      });
    }

    function subscribeToFeed(id, display) {
      log(`开始订阅用户 ${display} (${id}) 的流`);
      
      janus.attach({
        plugin: "janus.plugin.videoroom",
        success: function(pluginHandle) {
          const remoteFeed = pluginHandle;
          
          const subscribe = {
            request: "join",
            room: myroom,
            ptype: "subscriber",
            feed: id
          };
          
          remoteFeed.send({ message: subscribe });
        },
        error: function(error) {
          log(`订阅失败: ${error}`, 'error');
        },
        iceState: function(state) {
          log(`订阅ICE状态: ${state}`);
        },
        onmessage: function(msg, jsep) {
          log(`订阅消息: ${JSON.stringify(msg)}`);
          const event = msg["videoroom"];
          if (event === "attached") {
            log(`已成功订阅用户 ${display}`);
          }
          
          if (jsep) {
            log(`处理订阅SDP: ${jsep.type}`);
            remoteFeed.createAnswer({
              jsep: jsep,
              media: { audioSend: false, videoSend: false },
              success: function(jsep) {
                log('订阅应答创建成功');
                const body = { request: "start", room: myroom };
                remoteFeed.send({ message: body, jsep: jsep });
              },
              error: function(error) {
                log(`创建订阅应答失败: ${error}`, 'error');
              }
            });
          }
        },
        onremotestream: function(stream) {
          log(`收到 ${display} 的远程流`);
          document.getElementById('remoteVideo').srcObject = stream;
          updateStatus(`正在显示 ${display} 的视频`, 'success');
        }
      });
    }

    // 页面加载时检查Janus库
    window.onload = function() {
      updateStatus('页面已加载，请先检查Janus库', 'info');
      setTimeout(checkJanus, 1000);
    };
  </script>
</body>
</html>
