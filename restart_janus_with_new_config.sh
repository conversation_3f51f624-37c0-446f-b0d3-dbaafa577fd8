#!/bin/bash

echo "=== 重启 Janus 应用新配置 ==="
echo "时间: $(date)"
echo ""

# 检查当前 Janus 进程
echo "1. 检查当前 Janus 状态"
echo "======================"
if pgrep -f janus > /dev/null; then
    echo "✅ 发现 Janus 进程，准备停止..."
    pkill -f janus
    sleep 3
    
    # 确保完全停止
    if pgrep -f janus > /dev/null; then
        echo "⚠️  强制停止 Janus..."
        pkill -9 -f janus
        sleep 2
    fi
    echo "✅ Janus 已停止"
else
    echo "ℹ️  Janus 未运行"
fi
echo ""

# 验证配置文件
echo "2. 验证配置文件"
echo "==============="
CONFIG_FILES=(
    "/opt/janus/etc/janus/janus.jcfg"
    "/opt/janus/etc/janus/janus.plugin.videoroom.jcfg"
    "/opt/janus/etc/janus/janus.transport.websockets.jcfg"
)

for config in "${CONFIG_FILES[@]}"; do
    if [ -f "$config" ]; then
        echo "✅ $config 存在"
    else
        echo "❌ $config 不存在"
        exit 1
    fi
done
echo ""

# 检查关键配置更改
echo "3. 检查关键配置"
echo "==============="
echo "VideoRoom 房间 1000 比特率设置:"
grep -A 5 "room-1000" /opt/janus/etc/janus/janus.plugin.videoroom.jcfg | grep bitrate
echo ""

echo "媒体配置:"
grep -A 10 "^media:" /opt/janus/etc/janus/janus.jcfg | grep -E "min_nack_queue|twcc_period|no_media_timer"
echo ""

# 启动 Janus
echo "4. 启动 Janus"
echo "============="
cd /opt/janus

# 使用修改后的配置启动
echo "使用配置文件启动 Janus..."
nohup bin/janus -C etc/janus/janus.jcfg > janus.log 2>&1 &

# 等待启动
echo "等待 Janus 启动..."
sleep 5

# 检查启动状态
if pgrep -f janus > /dev/null; then
    echo "✅ Janus 启动成功！"
    
    # 检查端口监听
    echo ""
    echo "检查端口监听:"
    if netstat -tlnp | grep :8188; then
        echo "✅ WebSocket 端口 8188 正常监听"
    else
        echo "❌ WebSocket 端口 8188 未监听"
    fi
    
    if netstat -tlnp | grep :8088; then
        echo "✅ HTTP 端口 8088 正常监听"
    else
        echo "⚠️  HTTP 端口 8088 未监听"
    fi
    
else
    echo "❌ Janus 启动失败！"
    echo ""
    echo "检查日志:"
    tail -20 janus.log
    exit 1
fi

echo ""
echo "5. 配置更改总结"
echo "==============="
echo "✅ VideoRoom 比特率: 128kbps → 2Mbps"
echo "✅ 启用音频编解码器: opus"
echo "✅ 启用视频编解码器: vp8"
echo "✅ 增加 NACK 队列大小: 1000ms"
echo "✅ 启用传输宽带拥塞控制: 200ms"
echo "✅ 增加无媒体超时: 30s"
echo ""
echo "=== 重启完成 ==="
echo "现在可以测试视频流了！"
