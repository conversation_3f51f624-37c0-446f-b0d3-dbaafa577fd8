# WebRTC视频流问题诊断清单

## 问题描述
- ************ 能正常看到 *********** 的视频
- *********** 无法看到 ************ 的视频
- 服务器没有向 *********** 发送视频流

## 已进行的配置优化

### 1. Janus配置优化 (janus.jcfg)
- ✅ 启用了 `full_trickle = true`
- ✅ 设置了 `ice_nomination = "aggressive"`
- ✅ 配置了 `nat_1_1_mapping = "*************"`
- ✅ 添加了TURN服务器配置给Janus使用
- ✅ 设置了 `ice_enforce_list = "*************"`

### 2. TURN服务器配置优化 (turnserver.conf)
- ✅ 添加了 `external-ip=*************`
- ✅ 启用了 `lt-cred-mech` 长期凭证机制
- ✅ 添加了详细日志配置

### 3. 客户端配置优化
- ✅ 移除了强制relay策略，允许ICE自动选择最佳路径
- ✅ 添加了STUN服务器作为备选

## 诊断步骤

### 第一步：重启服务
1. 在服务器上运行 `restart_services.bat`（Windows）或 `restart_services.sh`（Linux）
2. 确认两个服务都正常启动
3. 检查端口监听状态

### 第二步：连接测试
1. 在两台客户端机器上打开 `test_webrtc_connectivity.html`
2. 依次执行所有测试：
   - STUN服务器测试
   - TURN服务器测试
   - ICE候选者收集测试
   - Janus连接测试
3. 记录测试结果

### 第三步：防火墙检查
确保服务器防火墙开放以下端口：
- 8188 (Janus WebSocket)
- 3478 (TURN)
- 20000-40000 (RTP媒体流)
- 49152-65535 (TURN relay)

### 第四步：网络路径分析
1. 从*********** ping *************
2. 从************ ping *************
3. 检查两台机器的NAT类型

### 第五步：详细日志分析
1. 查看TURN服务器日志：`/var/log/turnserver.log`
2. 查看Janus控制台输出
3. 查看浏览器开发者工具的网络和控制台日志

## 可能的原因分析

### 1. ICE连接问题
- **症状**: track状态显示为"ended"
- **可能原因**: ICE连接失败，无法建立媒体传输路径
- **解决方案**: 检查TURN服务器配置和网络连通性

### 2. NAT穿透问题
- **症状**: 一个方向能通，另一个方向不通
- **可能原因**: 不同的NAT类型导致穿透困难
- **解决方案**: 强制使用TURN中继

### 3. 防火墙阻塞
- **症状**: 连接建立但媒体流无法传输
- **可能原因**: RTP端口被防火墙阻塞
- **解决方案**: 开放RTP端口范围

### 4. Janus配置问题
- **症状**: SDP协商成功但媒体流不通
- **可能原因**: Janus无法正确路由媒体流
- **解决方案**: 检查NAT映射和接口配置

## 临时解决方案

如果问题仍然存在，可以尝试以下临时解决方案：

### 方案1：强制使用TURN中继
在客户端配置中恢复：
```javascript
iceTransportPolicy: "relay"
```

### 方案2：使用不同的STUN/TURN服务器
```javascript
iceServers: [
  { urls: "stun:stun.l.google.com:19302" },
  { urls: "stun:stun1.l.google.com:19302" },
  { urls: "turn:*************:3478", username: "hhzt", credential: "hhzt20130403" }
]
```

### 方案3：调整Janus媒体配置
在janus.jcfg中添加：
```
media: {
    rtp_port_range = "20000-40000"
    no_media_timer = 10
    slowlink_threshold = 8
}
```

## 下一步行动

1. **立即执行**: 重启服务并进行连接测试
2. **如果测试失败**: 检查防火墙和网络配置
3. **如果测试成功但问题仍存在**: 分析详细日志
4. **最后手段**: 联系网络管理员检查路由器/防火墙配置

## 联系信息
如需进一步协助，请提供：
1. 连接测试结果截图
2. TURN服务器日志
3. Janus服务器日志
4. 浏览器开发者工具的完整日志
