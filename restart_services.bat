@echo off
chcp 65001 >nul
echo === WebRTC服务重启脚本 ===
echo 当前时间: %date% %time%
echo 当前目录: %cd%

REM 检查管理员权限
net session >nul 2>&1
if %errorLevel% neq 0 (
    echo 请以管理员权限运行此脚本
    echo 右键点击脚本，选择"以管理员身份运行"
    pause
    exit /b 1
)

echo.
echo 1. 停止现有服务...

REM 停止Janus进程
echo 停止Janus服务...
taskkill /f /im janus.exe >nul 2>&1
if %errorLevel% equ 0 (
    echo Janus进程已停止
) else (
    echo Janus进程未运行或停止失败
)

REM 停止TURN服务器进程
echo 停止TURN服务器...
taskkill /f /im turnserver.exe >nul 2>&1
if %errorLevel% equ 0 (
    echo TURN服务器进程已停止
) else (
    echo TURN服务器进程未运行或停止失败
)

REM 等待进程完全停止
timeout /t 3 /nobreak >nul

echo.
echo 2. 检查端口占用情况...
echo 检查8188端口 (Janus WebSocket):
netstat -an | findstr :8188
if %errorLevel% neq 0 echo 端口8188未被占用

echo 检查3478端口 (TURN):
netstat -an | findstr :3478
if %errorLevel% neq 0 echo 端口3478未被占用

echo.
echo 3. 启动TURN服务器...
if exist "turnserver.conf" (
    start "TURN Server" turnserver.exe -c turnserver.conf -v
    echo TURN服务器启动命令已执行
    timeout /t 2 /nobreak >nul
) else (
    echo 错误: 找不到turnserver.conf文件
    pause
    exit /b 1
)

echo.
echo 4. 启动Janus服务器...
if exist "janus.jcfg" (
    start "Janus Server" janus.exe -C janus.jcfg
    echo Janus服务器启动命令已执行
    timeout /t 3 /nobreak >nul
) else (
    echo 错误: 找不到janus.jcfg文件
    pause
    exit /b 1
)

echo.
echo 5. 服务状态检查...
echo TURN服务器进程:
tasklist | findstr turnserver.exe
if %errorLevel% neq 0 echo TURN服务器进程未找到

echo Janus服务器进程:
tasklist | findstr janus.exe
if %errorLevel% neq 0 echo Janus服务器进程未找到

echo.
echo 端口监听状态:
echo 8188端口 (Janus WebSocket):
netstat -an | findstr :8188

echo 3478端口 (TURN):
netstat -an | findstr :3478

echo.
echo === 服务重启完成 ===
echo 请等待几秒钟让服务完全启动，然后测试连接
echo.
echo 测试建议:
echo 1. 打开 test_webrtc_connectivity.html 进行连接测试
echo 2. 检查防火墙设置，确保以下端口开放:
echo    - 8188 (Janus WebSocket)
echo    - 3478 (TURN)
echo    - 20000-40000 (RTP媒体流)
echo    - 49152-65535 (TURN relay)
echo 3. 如果问题仍然存在，检查服务器日志输出
echo.
pause
