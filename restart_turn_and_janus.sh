#!/bin/bash

echo "=== 重启 TURN 和 Janus 服务 ==="
echo "时间: $(date)"
echo ""

# 1. 停止服务
echo "1. 停止现有服务"
echo "================"

echo "停止 Janus..."
if pgrep -f janus > /dev/null; then
    pkill -f janus
    sleep 3
    echo "✅ Janus 已停止"
else
    echo "ℹ️  Janus 未运行"
fi

echo ""
echo "停止 TURN 服务器..."
if pgrep -f turnserver > /dev/null; then
    pkill -f turnserver
    sleep 3
    echo "✅ TURN 服务器已停止"
else
    echo "ℹ️  TURN 服务器未运行"
fi

# 2. 清理端口
echo ""
echo "2. 清理端口占用"
echo "================"
PORTS=(3478 3479 8188 8088)
for port in "${PORTS[@]}"; do
    if netstat -tlnp | grep ":$port " >/dev/null; then
        echo "⚠️  端口 $port 仍被占用，强制清理..."
        lsof -ti:$port | xargs kill -9 2>/dev/null
        sleep 1
    fi
done

# 3. 启动 TURN 服务器
echo ""
echo "3. 启动 TURN 服务器"
echo "=================="
cd /root/janus

echo "使用新配置启动 TURN 服务器..."
nohup turnserver -c turnserver.conf > turnserver.log 2>&1 &
sleep 3

if pgrep -f turnserver > /dev/null; then
    echo "✅ TURN 服务器启动成功"
    
    # 检查端口
    if netstat -tlnp | grep :3478; then
        echo "✅ TURN 端口 3478 正常监听"
    else
        echo "❌ TURN 端口 3478 未监听"
    fi
    
    if netstat -tlnp | grep :3479; then
        echo "✅ TURN 端口 3479 正常监听"
    else
        echo "⚠️  TURN 端口 3479 未监听"
    fi
else
    echo "❌ TURN 服务器启动失败"
    echo "错误日志:"
    tail -10 turnserver.log
    exit 1
fi

# 4. 启动 Janus
echo ""
echo "4. 启动 Janus"
echo "============="
cd /opt/janus

echo "启动 Janus..."
nohup bin/janus -c etc/janus/janus.jcfg > janus_restart.log 2>&1 &
sleep 5

if pgrep -f janus > /dev/null; then
    echo "✅ Janus 启动成功"
    
    # 检查端口
    if netstat -tlnp | grep :8188; then
        echo "✅ WebSocket 端口 8188 正常监听"
    else
        echo "❌ WebSocket 端口 8188 未监听"
    fi
    
    if netstat -tlnp | grep :8088; then
        echo "✅ HTTP 端口 8088 正常监听"
    else
        echo "⚠️  HTTP 端口 8088 未监听"
    fi
else
    echo "❌ Janus 启动失败"
    echo "错误日志:"
    tail -10 janus_restart.log
    exit 1
fi

# 5. 验证配置
echo ""
echo "5. 验证配置"
echo "==========="

echo "TURN 配置验证:"
echo "- 超时时间已增加"
echo "- mobility 已禁用"
echo "- 内网 IP 范围已允许"

echo ""
echo "Janus 配置验证:"
echo "- keep_private_host = true"
echo "- ice_enforce_list 已注释"

# 6. 测试连接
echo ""
echo "6. 测试基础连接"
echo "================"

echo "测试 TURN 服务器..."
timeout 5 curl -s "http://*************:8088/janus/info" | head -3

echo ""
echo "等待 10 秒后检查日志..."
sleep 10

echo ""
echo "TURN 服务器日志 (最近 5 行):"
tail -5 /root/janus/turnserver.log

echo ""
echo "Janus 日志 (最近 5 行):"
tail -5 janus_restart.log

echo ""
echo "=== 重启完成 ==="
echo ""
echo "现在请在 *********** 上重新测试 WebRTC 连接"
echo ""
echo "预期改进:"
echo "1. TURN 会话不再快速超时"
echo "2. ICE 候选者添加成功"
echo "3. SRTP 会话创建成功"
echo "4. *********** 能够接收视频流"
echo ""
echo "如果还有问题，请检查:"
echo "1. *********** 的网络配置"
echo "2. Windows 10 的防火墙设置"
echo "3. 浏览器的 WebRTC 设置"
