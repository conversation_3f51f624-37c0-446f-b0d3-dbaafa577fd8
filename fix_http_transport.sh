#!/bin/bash

echo "=== 修复 Janus HTTP 传输配置 ==="
echo "时间: $(date)"
echo ""

# 1. 查找 Janus 配置文件
echo "1. 查找 Janus 配置文件"
echo "===================="

JANUS_CONFIG_DIR=""
POSSIBLE_DIRS=(
    "/opt/janus/etc/janus"
    "/opt/janus/etc"
    "/usr/local/etc/janus"
    "/etc/janus"
)

for dir in "${POSSIBLE_DIRS[@]}"; do
    if [ -d "$dir" ]; then
        echo "找到配置目录: $dir"
        JANUS_CONFIG_DIR="$dir"
        break
    fi
done

if [ -z "$JANUS_CONFIG_DIR" ]; then
    echo "❌ 未找到 Janus 配置目录"
    exit 1
fi

echo "使用配置目录: $JANUS_CONFIG_DIR"
echo ""

# 2. 查找 HTTP 传输配置文件
echo "2. 查找 HTTP 传输配置文件"
echo "========================"

HTTP_CONFIG=""
POSSIBLE_FILES=(
    "$JANUS_CONFIG_DIR/janus.transport.http.jcfg"
    "$JANUS_CONFIG_DIR/janus.transport.http.cfg"
    "$JANUS_CONFIG_DIR/transports/janus.transport.http.jcfg"
)

for file in "${POSSIBLE_FILES[@]}"; do
    if [ -f "$file" ]; then
        echo "找到 HTTP 配置文件: $file"
        HTTP_CONFIG="$file"
        break
    fi
done

if [ -z "$HTTP_CONFIG" ]; then
    echo "❌ 未找到 HTTP 传输配置文件"
    echo "尝试创建默认配置..."
    
    HTTP_CONFIG="$JANUS_CONFIG_DIR/janus.transport.http.jcfg"
    cat > "$HTTP_CONFIG" << 'EOF'
# HTTP/HTTPS transport for Janus
general: {
    json = "indented"
    base_path = "/janus"
    http = true
    port = 8088
    interface = "0.0.0.0"  # 监听所有接口
    ip = "*************"   # 绑定到公网IP
    https = false
    secure_port = 8089
    admin_base_path = "/admin"
    admin_http = true
    admin_port = 7088
    admin_interface = "0.0.0.0"
    admin_ip = "*************"
    admin_https = false
    admin_secure_port = 7889
}

cors: {
    allow_origin = "*"
    allow_methods = "GET, POST, PUT, DELETE, OPTIONS"
    allow_headers = "Accept, Accept-Language, Content-Language, Content-Type"
}
EOF
    echo "✅ 创建了默认 HTTP 配置文件: $HTTP_CONFIG"
else
    echo "✅ 使用现有配置文件: $HTTP_CONFIG"
fi

echo ""

# 3. 备份并修改配置
echo "3. 修改 HTTP 配置"
echo "================"

# 备份原配置
cp "$HTTP_CONFIG" "$HTTP_CONFIG.backup.$(date +%H%M%S)"
echo "✅ 配置已备份"

# 检查当前配置
echo "当前 HTTP 配置:"
grep -E "interface|ip.*=|port.*=" "$HTTP_CONFIG" | head -10

echo ""
echo "修改配置以监听 IPv4..."

# 修改配置文件
sed -i 's/interface = "::"/interface = "0.0.0.0"/g' "$HTTP_CONFIG"
sed -i 's/interface = "::"/#interface = "::"/g' "$HTTP_CONFIG"
sed -i 's/#interface = "0.0.0.0"/interface = "0.0.0.0"/g' "$HTTP_CONFIG"

# 确保有正确的 IP 绑定
if ! grep -q 'ip = "*************"' "$HTTP_CONFIG"; then
    sed -i '/interface = "0.0.0.0"/a\    ip = "*************"' "$HTTP_CONFIG"
fi

echo "✅ 配置已修改"

echo ""
echo "修改后的配置:"
grep -E "interface|ip.*=|port.*=" "$HTTP_CONFIG" | head -10

echo ""

# 4. 重启 Janus
echo "4. 重启 Janus"
echo "============="

echo "停止 Janus..."
pkill -f janus
sleep 3

echo "启动 Janus..."
cd /opt/janus
nohup bin/janus -c etc/janus/janus.jcfg > janus_http_fixed.log 2>&1 &

sleep 5

# 5. 验证修复
echo ""
echo "5. 验证修复结果"
echo "================"

echo "检查端口监听:"
netstat -tlnp | grep -E ":8088|:8188"

echo ""
echo "检查 IPv4 监听:"
if netstat -tlnp | grep "0.0.0.0:8088"; then
    echo "✅ HTTP 端口 8088 正在监听 IPv4"
else
    echo "❌ HTTP 端口 8088 未监听 IPv4"
fi

echo ""
echo "测试 HTTP 连接:"
if curl -s "http://*************:8088/janus/info" | head -3; then
    echo "✅ HTTP 连接测试成功"
else
    echo "❌ HTTP 连接测试失败"
fi

echo ""
echo "Janus 日志 (最近 10 行):"
tail -10 janus_http_fixed.log

echo ""
echo "=== 修复完成 ==="
echo ""
echo "现在可以通过以下地址访问:"
echo "- WebSocket: ws://*************:8188/"
echo "- HTTP API: http://*************:8088/janus/"
echo "- 测试页面: http://*************:8088/simple_videoroom_client.html"
