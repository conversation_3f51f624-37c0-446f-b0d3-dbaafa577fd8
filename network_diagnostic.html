<!DOCTYPE html>
<html>
<head>
  <title>网络诊断工具</title>
  <style>
    body { font-family: Arial, sans-serif; margin: 20px; }
    .test-item { margin: 10px 0; padding: 10px; border: 1px solid #ccc; }
    .success { background-color: #d4edda; }
    .error { background-color: #f8d7da; }
    .warning { background-color: #fff3cd; }
    button { padding: 10px 20px; margin: 5px; }
    #results { height: 400px; overflow-y: scroll; border: 1px solid #ccc; padding: 10px; font-family: monospace; }
  </style>
</head>
<body>
  <h1>*********** 网络诊断工具</h1>
  
  <div>
    <button onclick="testBasicConnectivity()">测试基础连接</button>
    <button onclick="testWebRTCCapability()">测试 WebRTC 能力</button>
    <button onclick="testSTUNServers()">测试 STUN 服务器</button>
    <button onclick="clearResults()">清除结果</button>
  </div>
  
  <div id="results"></div>

  <script>
    function log(message) {
      const timestamp = new Date().toLocaleTimeString();
      const results = document.getElementById('results');
      results.innerHTML += `[${timestamp}] ${message}<br>`;
      results.scrollTop = results.scrollHeight;
      console.log(message);
    }
    
    function clearResults() {
      document.getElementById('results').innerHTML = '';
    }
    
    async function testBasicConnectivity() {
      log('🔍 开始基础连接测试...');
      
      // 测试 HTTP 连接
      try {
        const response = await fetch('http://*************:8088/janus/info');
        const data = await response.json();
        log('✅ HTTP 连接成功: ' + JSON.stringify(data).substring(0, 100) + '...');
      } catch (error) {
        log('❌ HTTP 连接失败: ' + error.message);
      }
      
      // 测试 WebSocket 连接
      try {
        const ws = new WebSocket('ws://*************:8188/');
        ws.onopen = function() {
          log('✅ WebSocket 连接成功');
          ws.close();
        };
        ws.onerror = function() {
          log('❌ WebSocket 连接失败');
        };
        ws.onclose = function(event) {
          log(`ℹ️ WebSocket 关闭: code=${event.code}, reason=${event.reason}`);
        };
      } catch (error) {
        log('❌ WebSocket 创建失败: ' + error.message);
      }
      
      // 获取客户端网络信息
      try {
        const connection = navigator.connection || navigator.mozConnection || navigator.webkitConnection;
        if (connection) {
          log(`📶 网络类型: ${connection.effectiveType || connection.type}`);
          log(`📊 下行速度: ${connection.downlink || 'unknown'} Mbps`);
        }
      } catch (error) {
        log('⚠️ 无法获取网络信息');
      }
    }
    
    async function testWebRTCCapability() {
      log('🔍 开始 WebRTC 能力测试...');
      
      // 检查 WebRTC 支持
      if (!window.RTCPeerConnection) {
        log('❌ 浏览器不支持 WebRTC');
        return;
      }
      log('✅ 浏览器支持 WebRTC');
      
      // 测试摄像头访问
      try {
        const stream = await navigator.mediaDevices.getUserMedia({ video: true, audio: false });
        log('✅ 摄像头访问成功');
        stream.getTracks().forEach(track => track.stop());
      } catch (error) {
        log('❌ 摄像头访问失败: ' + error.message);
      }
      
      // 测试 ICE 候选者收集
      const pc = new RTCPeerConnection({
        iceServers: [
          { urls: 'stun:stun.l.google.com:19302' }
        ]
      });
      
      let candidateCount = 0;
      pc.onicecandidate = function(event) {
        if (event.candidate) {
          candidateCount++;
          log(`🧊 ICE 候选者 ${candidateCount}: ${event.candidate.candidate.substring(0, 50)}...`);
        } else {
          log(`✅ ICE 候选者收集完成，共 ${candidateCount} 个`);
          pc.close();
        }
      };
      
      pc.onicegatheringstatechange = function() {
        log(`🔄 ICE 收集状态: ${pc.iceGatheringState}`);
      };
      
      // 创建 offer 触发候选者收集
      try {
        const offer = await pc.createOffer();
        await pc.setLocalDescription(offer);
        log('✅ 创建 offer 成功，开始收集 ICE 候选者...');
      } catch (error) {
        log('❌ 创建 offer 失败: ' + error.message);
        pc.close();
      }
    }
    
    async function testSTUNServers() {
      log('🔍 开始 STUN 服务器测试...');
      
      const stunServers = [
        'stun:stun.l.google.com:19302',
        'stun:stun1.l.google.com:19302',
        'stun:stun.cloudflare.com:3478',
        'stun:*************:3478'
      ];
      
      for (const stunUrl of stunServers) {
        await testSingleSTUN(stunUrl);
      }
    }
    
    function testSingleSTUN(stunUrl) {
      return new Promise((resolve) => {
        log(`🔍 测试 STUN 服务器: ${stunUrl}`);
        
        const pc = new RTCPeerConnection({
          iceServers: [{ urls: stunUrl }]
        });
        
        let resolved = false;
        const timeout = setTimeout(() => {
          if (!resolved) {
            log(`⏰ ${stunUrl} 超时`);
            pc.close();
            resolved = true;
            resolve();
          }
        }, 10000);
        
        pc.onicecandidate = function(event) {
          if (event.candidate && event.candidate.candidate.includes('srflx')) {
            log(`✅ ${stunUrl} 工作正常，获得公网地址: ${event.candidate.candidate.split(' ')[4]}`);
            clearTimeout(timeout);
            pc.close();
            if (!resolved) {
              resolved = true;
              resolve();
            }
          }
        };
        
        pc.onicegatheringstatechange = function() {
          if (pc.iceGatheringState === 'complete' && !resolved) {
            log(`⚠️ ${stunUrl} 完成但未获得公网地址`);
            clearTimeout(timeout);
            pc.close();
            resolved = true;
            resolve();
          }
        };
        
        // 创建 offer 触发 STUN 测试
        pc.createOffer().then(offer => {
          return pc.setLocalDescription(offer);
        }).catch(error => {
          log(`❌ ${stunUrl} 测试失败: ${error.message}`);
          clearTimeout(timeout);
          pc.close();
          if (!resolved) {
            resolved = true;
            resolve();
          }
        });
      });
    }
    
    // 页面加载时显示基本信息
    window.onload = function() {
      log('📱 用户代理: ' + navigator.userAgent);
      log('🌐 当前地址: ' + window.location.href);
      log('🔧 准备进行网络诊断...');
    };
  </script>
</body>
</html>
