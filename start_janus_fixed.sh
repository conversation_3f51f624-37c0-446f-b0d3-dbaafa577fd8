#!/bin/bash

echo "=== 启动修复后的 Janus ==="
echo "时间: $(date)"
echo ""

# 1. 生成证书
echo "1. 生成 DTLS 证书..."
chmod +x generate_certificates.sh
./generate_certificates.sh

if [ $? -ne 0 ]; then
    echo "❌ 证书生成失败"
    exit 1
fi
echo ""

# 2. 停止现有的 Janus 进程
echo "2. 停止现有 Janus 进程..."
if pgrep -f janus > /dev/null; then
    echo "发现 Janus 进程，正在停止..."
    pkill -f janus
    sleep 3
    
    if pgrep -f janus > /dev/null; then
        echo "强制停止..."
        pkill -9 -f janus
        sleep 2
    fi
    echo "✅ Janus 已停止"
else
    echo "ℹ️  Janus 未运行"
fi
echo ""

# 3. 检查端口
echo "3. 检查端口占用..."
if netstat -tlnp | grep :8188; then
    echo "⚠️  端口 8188 仍被占用"
    lsof -ti:8188 | xargs kill -9 2>/dev/null
    sleep 1
fi

if netstat -tlnp | grep :8088; then
    echo "⚠️  端口 8088 仍被占用"
    lsof -ti:8088 | xargs kill -9 2>/dev/null
    sleep 1
fi
echo ""

# 4. 启动 Janus
echo "4. 启动 Janus..."
cd /opt/janus

# 首先尝试使用修复后的主配置
echo "使用主配置文件启动..."
nohup bin/janus -c etc/janus/janus.jcfg > janus_main.log 2>&1 &
MAIN_PID=$!

# 等待启动
sleep 5

# 检查主配置启动是否成功
if kill -0 $MAIN_PID 2>/dev/null && netstat -tlnp | grep :8188 >/dev/null; then
    echo "✅ 使用主配置启动成功！"
    JANUS_PID=$MAIN_PID
    CONFIG_USED="main"
else
    echo "⚠️  主配置启动失败，尝试调试配置..."
    kill $MAIN_PID 2>/dev/null
    
    # 尝试使用调试配置
    nohup bin/janus -c /root/janus/janus_debug.jcfg > janus_debug.log 2>&1 &
    DEBUG_PID=$!
    
    sleep 5
    
    if kill -0 $DEBUG_PID 2>/dev/null && netstat -tlnp | grep :8188 >/dev/null; then
        echo "✅ 使用调试配置启动成功！"
        JANUS_PID=$DEBUG_PID
        CONFIG_USED="debug"
    else
        echo "❌ 两种配置都启动失败"
        kill $DEBUG_PID 2>/dev/null
        
        echo ""
        echo "主配置错误日志:"
        tail -10 janus_main.log
        echo ""
        echo "调试配置错误日志:"
        tail -10 janus_debug.log
        exit 1
    fi
fi

echo ""
echo "5. 验证启动状态..."
echo "使用的配置: $CONFIG_USED"
echo "进程 ID: $JANUS_PID"

# 检查端口监听
echo ""
echo "端口监听状态:"
if netstat -tlnp | grep :8188; then
    echo "✅ WebSocket 端口 8188 正常"
else
    echo "❌ WebSocket 端口 8188 异常"
fi

if netstat -tlnp | grep :8088; then
    echo "✅ HTTP 端口 8088 正常"
else
    echo "⚠️  HTTP 端口 8088 未监听"
fi

# 检查 RTP 端口
RTP_COUNT=$(netstat -unlp | grep -E ":2[0-9][0-9][0-9][0-9]" | wc -l)
echo "RTP 端口监听数量: $RTP_COUNT"

echo ""
echo "6. 测试 WebSocket 连接..."
timeout 5 curl -s "http://60.255.197.32:8088/janus/info" | head -3

echo ""
echo "=== Janus 启动完成 ==="
echo ""
echo "现在可以测试 WebRTC 连接了！"
echo "如果还有问题，请检查日志:"
echo "  主配置日志: /opt/janus/janus_main.log"
echo "  调试配置日志: /opt/janus/janus_debug.log"
