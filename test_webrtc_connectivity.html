<!DOCTYPE html>
<html>
<head>
  <meta charset="UTF-8">
  <title>WebRTC连接测试工具</title>
  <style>
    body { font-family: Arial, sans-serif; margin: 20px; }
    .test-section { margin: 20px 0; padding: 15px; border: 1px solid #ccc; border-radius: 5px; }
    .result { margin: 10px 0; padding: 10px; border-radius: 3px; }
    .success { background-color: #d4edda; color: #155724; }
    .error { background-color: #f8d7da; color: #721c24; }
    .info { background-color: #d1ecf1; color: #0c5460; }
    button { padding: 10px 20px; margin: 5px; }
    #log { height: 300px; overflow-y: auto; border: 1px solid #ccc; padding: 10px; background: #f9f9f9; }
  </style>
</head>
<body>
  <h1>WebRTC连接测试工具</h1>
  
  <div class="test-section">
    <h3>1. STUN/TURN服务器测试</h3>
    <button onclick="testSTUN()">测试STUN服务器</button>
    <button onclick="testTURN()">测试TURN服务器</button>
    <div id="stunResult"></div>
    <div id="turnResult"></div>
  </div>

  <div class="test-section">
    <h3>2. ICE候选者收集测试</h3>
    <button onclick="testICEGathering()">收集ICE候选者</button>
    <div id="iceResult"></div>
  </div>

  <div class="test-section">
    <h3>3. Janus连接测试</h3>
    <button onclick="testJanusConnection()">测试Janus连接</button>
    <div id="janusResult"></div>
  </div>

  <div class="test-section">
    <h3>4. 详细日志</h3>
    <button onclick="clearLog()">清空日志</button>
    <div id="log"></div>
  </div>

  <script>
    function log(message, type = 'info') {
      const logDiv = document.getElementById('log');
      const timestamp = new Date().toLocaleTimeString();
      logDiv.innerHTML += `<div class="${type}">[${timestamp}] ${message}</div>`;
      logDiv.scrollTop = logDiv.scrollHeight;
      console.log(message);
    }

    function clearLog() {
      document.getElementById('log').innerHTML = '';
    }

    function showResult(elementId, message, isSuccess) {
      const element = document.getElementById(elementId);
      element.innerHTML = `<div class="result ${isSuccess ? 'success' : 'error'}">${message}</div>`;
    }

    async function testSTUN() {
      log('开始测试STUN服务器...');
      try {
        const pc = new RTCPeerConnection({
          iceServers: [{ urls: 'stun:stun.l.google.com:19302' }]
        });
        
        const candidates = [];
        pc.onicecandidate = (event) => {
          if (event.candidate) {
            candidates.push(event.candidate);
            log(`收到ICE候选者: ${event.candidate.candidate}`);
          } else {
            log('ICE候选者收集完成');
            const srflxCandidates = candidates.filter(c => c.candidate.includes('srflx'));
            if (srflxCandidates.length > 0) {
              showResult('stunResult', `STUN测试成功！收集到${srflxCandidates.length}个srflx候选者`, true);
            } else {
              showResult('stunResult', 'STUN测试失败：未收集到srflx候选者', false);
            }
            pc.close();
          }
        };

        await pc.createOffer();
      } catch (error) {
        log(`STUN测试错误: ${error.message}`, 'error');
        showResult('stunResult', `STUN测试失败: ${error.message}`, false);
      }
    }

    async function testTURN() {
      log('开始测试TURN服务器...');
      try {
        const pc = new RTCPeerConnection({
          iceServers: [
            { 
              urls: ['turn:*************:3478?transport=udp', 'turn:*************:3478?transport=tcp'], 
              username: 'hhzt', 
              credential: 'hhzt20130403' 
            }
          ],
          iceTransportPolicy: 'relay'
        });
        
        const candidates = [];
        pc.onicecandidate = (event) => {
          if (event.candidate) {
            candidates.push(event.candidate);
            log(`收到TURN候选者: ${event.candidate.candidate}`);
          } else {
            log('TURN候选者收集完成');
            const relayCandidates = candidates.filter(c => c.candidate.includes('relay'));
            if (relayCandidates.length > 0) {
              showResult('turnResult', `TURN测试成功！收集到${relayCandidates.length}个relay候选者`, true);
            } else {
              showResult('turnResult', 'TURN测试失败：未收集到relay候选者', false);
            }
            pc.close();
          }
        };

        await pc.createOffer();
      } catch (error) {
        log(`TURN测试错误: ${error.message}`, 'error');
        showResult('turnResult', `TURN测试失败: ${error.message}`, false);
      }
    }

    async function testICEGathering() {
      log('开始ICE候选者收集测试...');
      try {
        const pc = new RTCPeerConnection({
          iceServers: [
            { urls: 'stun:stun.l.google.com:19302' },
            { 
              urls: ['turn:*************:3478?transport=udp', 'turn:*************:3478?transport=tcp'], 
              username: 'hhzt', 
              credential: 'hhzt20130403' 
            }
          ]
        });
        
        const candidates = [];
        pc.onicecandidate = (event) => {
          if (event.candidate) {
            candidates.push(event.candidate);
            log(`ICE候选者: ${event.candidate.candidate}`);
          } else {
            log('ICE候选者收集完成');
            const hostCandidates = candidates.filter(c => c.candidate.includes('host'));
            const srflxCandidates = candidates.filter(c => c.candidate.includes('srflx'));
            const relayCandidates = candidates.filter(c => c.candidate.includes('relay'));
            
            const summary = `
              Host候选者: ${hostCandidates.length}个<br>
              Srflx候选者: ${srflxCandidates.length}个<br>
              Relay候选者: ${relayCandidates.length}个<br>
              总计: ${candidates.length}个
            `;
            showResult('iceResult', summary, candidates.length > 0);
            pc.close();
          }
        };

        await pc.createOffer();
      } catch (error) {
        log(`ICE收集测试错误: ${error.message}`, 'error');
        showResult('iceResult', `ICE收集测试失败: ${error.message}`, false);
      }
    }

    async function testJanusConnection() {
      log('开始测试Janus连接...');
      try {
        const ws = new WebSocket('ws://*************:8188', 'janus-protocol');
        
        ws.onopen = () => {
          log('WebSocket连接成功');
          showResult('janusResult', 'Janus WebSocket连接成功', true);
          ws.close();
        };
        
        ws.onerror = (error) => {
          log(`WebSocket连接错误: ${error}`, 'error');
          showResult('janusResult', 'Janus WebSocket连接失败', false);
        };
        
        ws.onclose = (event) => {
          log(`WebSocket连接关闭: code=${event.code}, reason=${event.reason}`);
        };
        
      } catch (error) {
        log(`Janus连接测试错误: ${error.message}`, 'error');
        showResult('janusResult', `Janus连接测试失败: ${error.message}`, false);
      }
    }

    // 页面加载时显示当前IP信息
    window.onload = async () => {
      try {
        const response = await fetch('https://api.ipify.org?format=json');
        const data = await response.json();
        log(`当前公网IP: ${data.ip}`, 'info');
      } catch (error) {
        log('无法获取公网IP信息', 'error');
      }
    };
  </script>
</body>
</html>
