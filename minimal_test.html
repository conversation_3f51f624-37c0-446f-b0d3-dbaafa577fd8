<!DOCTYPE html>
<html>
<head>
  <meta charset="UTF-8">
  <title>最小化测试 - 强制TURN中继</title>
  <style>
    body { font-family: Arial, sans-serif; margin: 20px; }
    .video-container { display: flex; gap: 20px; margin: 20px 0; }
    video { width: 320px; height: 240px; border: 1px solid #ccc; }
    button { padding: 10px 20px; margin: 5px; }
    .status { margin: 10px 0; padding: 10px; border-radius: 3px; }
    .success { background-color: #d4edda; color: #155724; }
    .error { background-color: #f8d7da; color: #721c24; }
    .info { background-color: #d1ecf1; color: #0c5460; }
    #log { height: 200px; overflow-y: auto; border: 1px solid #ccc; padding: 10px; background: #f9f9f9; font-family: monospace; font-size: 12px; }
  </style>
</head>
<body>
  <h1>Janus VideoRoom 测试 (仅视频版本)</h1>
  
  <button onclick="startTest()">开始测试</button>
  <button onclick="clearLog()">清空日志</button>
  <button onclick="playRemoteVideo()">手动播放远程视频</button>
  <button onclick="diagnoseNetwork()">网络诊断</button>
  <button onclick="listRooms()">列出房间</button>
  <button onclick="getRoomInfo()">查看房间信息</button>
  <button onclick="getJanusInfo()">查看Janus信息</button>
  <button onclick="createRoomViaAdmin()">通过Admin API创建房间</button>
  <button onclick="resubscribeAll()">重新订阅所有发布者</button>
  <button onclick="testDirectConnection()">测试直连模式</button>
  <button onclick="forceRestoreVideo()">强制恢复视频</button>
  <button onclick="debugVideoStream()">调试视频流</button>
  <button onclick="testVideoDisplay()">测试视频显示</button>
  <button onclick="checkMediaSending()">检查媒体发送</button>

  <div style="margin: 10px 0;">
    <input type="text" id="pinInput" placeholder="输入房间PIN码 (常见: 1234, 0000, admin)" style="padding: 5px; width: 200px;">
    <button onclick="joinWithPin()">使用PIN码加入</button>
    <button onclick="tryCommonPins()">尝试常见PIN</button>
    <button onclick="joinWithEmptyPin()">尝试空PIN</button>
  </div>

  <div id="status" class="status info">准备就绪</div>

  <div class="video-container">
    <div>
      <h3>本地视频</h3>
      <video id="localVideo" autoplay muted></video>
    </div>
    <div>
      <h3>远程视频</h3>
      <video id="remoteVideo" autoplay></video>
    </div>
  </div>

  <div>
    <h3>日志</h3>
    <div id="log"></div>
  </div>

  <!-- 按正确顺序引入依赖 -->
  <script src="adapter-latest.js"></script>
  <script src="janus.js"></script>
  
  <script>
    let janus = null;
    let videoroom = null;
    let localStream = null;
    let subscribedFeeds = new Set(); // 跟踪已订阅的feed
    let savedRemoteStream = null; // 保存远程流引用，防止被清理
    const ROOM_ID = 1234; // 房间ID - 使用无PIN的测试房间

    function log(message) {
      const logDiv = document.getElementById('log');
      const timestamp = new Date().toLocaleTimeString();
      logDiv.innerHTML += `<div>[${timestamp}] ${message}</div>`;
      logDiv.scrollTop = logDiv.scrollHeight;
      console.log(message);
    }

    function updateStatus(message, type = 'info') {
      const statusDiv = document.getElementById('status');
      statusDiv.className = `status ${type}`;
      statusDiv.textContent = message;
      log(message);
    }

    function clearLog() {
      document.getElementById('log').innerHTML = '';
    }

    function playRemoteVideo() {
      const remoteVideo = document.getElementById('remoteVideo');

      // 如果当前没有流但有保存的流，先恢复
      if ((!remoteVideo.srcObject || !remoteVideo.srcObject.getTracks().length) && savedRemoteStream) {
        log('🔄 恢复保存的远程流');
        remoteVideo.srcObject = savedRemoteStream;
      }

      if (remoteVideo && remoteVideo.srcObject) {
        // 确保配置正确
        remoteVideo.autoplay = true;
        remoteVideo.playsInline = true;
        remoteVideo.muted = false;

        remoteVideo.play().then(() => {
          log('✅ 手动播放远程视频成功');
          updateStatus('远程视频正在播放', 'success');
        }).catch(e => {
          log(`❌ 手动播放失败: ${e.message}`);
          updateStatus(`播放失败: ${e.message}`, 'error');
        });
      } else {
        log('❌ 没有远程视频流可播放');
        updateStatus('没有远程视频流', 'error');
      }
    }

    function diagnoseNetwork() {
      log('🔍 开始网络诊断...');

      // 检查TURN服务器连通性
      const turnServer = 'turn:*************:3478';
      log(`📡 测试TURN服务器: ${turnServer}`);

      // 创建测试PeerConnection
      const testPC = new RTCPeerConnection({
        iceServers: [
          { urls: turnServer, username: "hhzt", credential: "hhzt20130403" }
        ],
        iceTransportPolicy: "relay"
      });

      testPC.onicecandidate = (event) => {
        if (event.candidate) {
          log(`🔗 ICE候选: ${event.candidate.candidate}`);
          if (event.candidate.candidate.includes('relay')) {
            log('✅ TURN中继候选可用');
          }
        } else {
          log('🏁 ICE候选收集完成');
        }
      };

      testPC.onicegatheringstatechange = () => {
        log(`📊 ICE收集状态: ${testPC.iceGatheringState}`);
      };

      // 创建数据通道触发ICE收集
      testPC.createDataChannel('test');
      testPC.createOffer().then(offer => {
        return testPC.setLocalDescription(offer);
      }).then(() => {
        log('🚀 开始ICE候选收集...');
        setTimeout(() => {
          testPC.close();
          log('🔍 网络诊断完成');
        }, 5000);
      }).catch(e => {
        log(`❌ 网络诊断失败: ${e.message}`);
      });
    }

    function listRooms() {
      if (!videoroom) {
        log('❌ VideoRoom插件未连接');
        return;
      }

      log('📋 查询现有房间...');
      const listRequest = { request: "list" };

      videoroom.send({
        message: listRequest,
        success: function(result) {
          if (result && result.list) {
            log(`📋 找到 ${result.list.length} 个房间:`);
            result.list.forEach(room => {
              log(`  - 房间 ${room.room}: ${room.description} (${room.num_participants} 人)`);
            });
          } else {
            log('📋 没有找到房间');
          }
        },
        error: function(error) {
          log(`❌ 查询房间失败: ${error}`);
        }
      });
    }

    function getRoomInfo() {
      if (!videoroom) {
        log('❌ VideoRoom插件未连接');
        return;
      }

      log(`🔍 查询房间 ${ROOM_ID} 信息...`);
      const infoRequest = {
        request: "exists",
        room: ROOM_ID
      };

      videoroom.send({
        message: infoRequest,
        success: function(result) {
          log(`📋 房间 ${ROOM_ID} 信息:`);
          log(`  - 存在: ${result.exists ? '是' : '否'}`);
          if (result.exists) {
            log(`  - 房间ID: ${result.room}`);
            if (result.description) log(`  - 描述: ${result.description}`);
            if (result.pin_required !== undefined) log(`  - 需要PIN: ${result.pin_required ? '是' : '否'}`);
            if (result.max_publishers !== undefined) log(`  - 最大发布者: ${result.max_publishers}`);
            if (result.bitrate !== undefined) log(`  - 比特率: ${result.bitrate}`);
          }
        },
        error: function(error) {
          log(`❌ 查询房间信息失败: ${error}`);
        }
      });
    }

    async function createRoomViaAdmin() {
      log('🔧 尝试通过Admin API创建房间...');

      const adminUrl = 'http://*************:7088/admin';
      const adminSecret = 'janusoverlord'; // 从配置文件中获取

      const createRoomRequest = {
        janus: "message_plugin",
        admin_secret: adminSecret,
        transaction: Math.random().toString(36).substring(7),
        plugin: "janus.plugin.videoroom",
        request: {
          request: "create",
          room: ROOM_ID,
          description: "通过Admin API创建的测试房间",
          publishers: 10,
          bitrate: 256000,
          fir_freq: 10,
          audiocodec: "opus",
          videocodec: "vp8",
          record: false,
          is_private: false
        }
      };

      try {
        const response = await fetch(adminUrl, {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
          },
          body: JSON.stringify(createRoomRequest)
        });

        const result = await response.json();
        log(`Admin API响应: ${JSON.stringify(result)}`);

        if (result.janus === 'success') {
          log(`✅ 房间 ${ROOM_ID} 通过Admin API创建成功`);
          updateStatus(`房间创建成功，可以开始测试`, 'success');
        } else {
          log(`❌ Admin API创建房间失败: ${result.error || '未知错误'}`);
        }
      } catch (error) {
        log(`❌ Admin API请求失败: ${error.message}`);
        updateStatus(`Admin API请求失败: ${error.message}`, 'error');
      }
    }

    function joinWithPin() {
      const pin = document.getElementById('pinInput').value.trim();
      if (!pin) {
        updateStatus('请输入PIN码', 'error');
        return;
      }

      if (!videoroom) {
        updateStatus('请先连接VideoRoom', 'error');
        return;
      }

      log(`🔑 尝试使用PIN码加入房间: ${pin}`);
      joinRoom(pin);
    }

    function tryCommonPins() {
      const commonPins = ['1234', '0000', 'admin', '', 'test', '1111'];
      let currentIndex = 0;

      function tryNextPin() {
        if (currentIndex >= commonPins.length) {
          log('❌ 所有常见PIN码都尝试过了');
          updateStatus('所有常见PIN码都失败，请手动输入正确的PIN码', 'error');
          return;
        }

        const pin = commonPins[currentIndex];
        log(`🔑 尝试常见PIN码 ${currentIndex + 1}/${commonPins.length}: "${pin}"`);
        currentIndex++;

        // 设置一个标志来跟踪这次尝试
        window.tryingCommonPin = true;
        joinRoom(pin);
      }

      if (!videoroom) {
        updateStatus('请先连接VideoRoom', 'error');
        return;
      }

      log('🔍 开始尝试常见PIN码...');
      tryNextPin();
    }

    function joinWithEmptyPin() {
      if (!videoroom) {
        updateStatus('请先连接VideoRoom', 'error');
        return;
      }

      log('🔑 尝试使用空PIN码加入房间...');
      joinRoom("");
    }

    function getJanusInfo() {
      if (!janus) {
        log('❌ Janus未连接');
        return;
      }

      log('🔍 查询Janus服务器信息...');
      janus.getInfo({
        success: function(info) {
          log('📋 Janus服务器信息:');
          log(`  - 版本: ${info.version_string || info.version}`);
          log(`  - 名称: ${info.name}`);
          log(`  - 作者: ${info.author}`);
          if (info.plugins) {
            log(`  - 插件: ${Object.keys(info.plugins).join(', ')}`);
          }
          if (info.transports) {
            log(`  - 传输: ${Object.keys(info.transports).join(', ')}`);
          }
        },
        error: function(error) {
          log(`❌ 获取Janus信息失败: ${error}`);
        }
      });
    }

    function resubscribeAll() {
      if (!videoroom) {
        log('❌ VideoRoom插件未连接');
        return;
      }

      log('🔄 重新订阅所有发布者...');

      // 清空已订阅列表
      subscribedFeeds.clear();

      // 查询当前房间的发布者
      const listRequest = {
        request: "listparticipants",
        room: ROOM_ID
      };

      videoroom.send({
        message: listRequest,
        success: function(result) {
          if (result && result.participants) {
            log(`📋 找到 ${result.participants.length} 个参与者`);
            result.participants.forEach(participant => {
              if (participant.publisher === true) {
                log(`🔄 重新订阅发布者: ${participant.display} (${participant.id})`);
                subscribeToFeed(participant.id, participant.display);
              }
            });
          } else {
            log('📋 没有找到参与者');
          }
        },
        error: function(error) {
          log(`❌ 查询参与者失败: ${error}`);
        }
      });
    }

    function testDirectConnection() {
      log('🔧 测试直连模式（无TURN）...');

      // 重新初始化Janus，使用直连配置
      if (janus) {
        janus.destroy();
      }

      setTimeout(() => {
        janus = new Janus({
          server: "ws://*************:8188/",
          // 只使用STUN，不使用TURN
          iceServers: [
            { urls: "stun:stun.l.google.com:19302" }
          ],
          success: function() {
            updateStatus('Janus直连模式连接成功', 'success');
            log('✅ 直连模式初始化成功，请重新开始测试');
          },
          error: function(error) {
            updateStatus(`直连模式失败: ${error}`, 'error');
          }
        });
      }, 1000);
    }

    function forceRestoreVideo() {
      log('🔧 强制恢复视频流...');

      const remoteVideo = document.getElementById('remoteVideo');
      if (!remoteVideo) {
        log('❌ 找不到远程视频元素');
        return;
      }

      // 如果有保存的流，恢复它
      if (savedRemoteStream) {
        log('📺 恢复保存的远程流');
        remoteVideo.srcObject = savedRemoteStream;

        // 确保视频配置正确
        remoteVideo.autoplay = true;
        remoteVideo.playsInline = true;
        remoteVideo.muted = false;
        remoteVideo.controls = true; // 添加控制条便于调试

        // 尝试播放
        setTimeout(() => {
          remoteVideo.play().then(() => {
            log('✅ 强制恢复视频成功！');
            updateStatus('视频流已恢复', 'success');
          }).catch(e => {
            log(`❌ 强制恢复失败: ${e.message}`);
            log('💡 请手动点击视频播放按钮');
          });
        }, 100);
      } else {
        log('❌ 没有保存的远程流可恢复');
        updateStatus('没有可恢复的视频流', 'error');
      }
    }

    function debugVideoStream() {
      log('🔍 开始调试视频流状态...');

      const localVideo = document.getElementById('localVideo');
      const remoteVideo = document.getElementById('remoteVideo');

      // 检查本地视频
      log('📹 本地视频状态:');
      if (localVideo.srcObject) {
        const localTracks = localVideo.srcObject.getTracks();
        log(`  - 轨道数量: ${localTracks.length}`);
        localTracks.forEach((track, index) => {
          log(`  - 轨道 ${index}: ${track.kind}, enabled: ${track.enabled}, readyState: ${track.readyState}`);
        });
        log(`  - 视频尺寸: ${localVideo.videoWidth}x${localVideo.videoHeight}`);
        log(`  - 播放状态: paused=${localVideo.paused}, ended=${localVideo.ended}`);
      } else {
        log('  - 没有本地视频流');
      }

      // 检查远程视频
      log('📺 远程视频状态:');
      if (remoteVideo.srcObject) {
        const remoteTracks = remoteVideo.srcObject.getTracks();
        log(`  - 轨道数量: ${remoteTracks.length}`);
        remoteTracks.forEach((track, index) => {
          log(`  - 轨道 ${index}: ${track.kind}, enabled: ${track.enabled}, readyState: ${track.readyState}, muted: ${track.muted}`);
        });
        log(`  - 视频尺寸: ${remoteVideo.videoWidth}x${remoteVideo.videoHeight}`);
        log(`  - 播放状态: paused=${remoteVideo.paused}, ended=${remoteVideo.ended}`);
        log(`  - 视频元素属性: autoplay=${remoteVideo.autoplay}, muted=${remoteVideo.muted}`);
      } else {
        log('  - 没有远程视频流');
      }

      // 检查保存的流
      if (savedRemoteStream) {
        log('💾 保存的远程流状态:');
        const savedTracks = savedRemoteStream.getTracks();
        log(`  - 轨道数量: ${savedTracks.length}`);
        savedTracks.forEach((track, index) => {
          log(`  - 轨道 ${index}: ${track.kind}, enabled: ${track.enabled}, readyState: ${track.readyState}, muted: ${track.muted}`);
        });
      } else {
        log('💾 没有保存的远程流');
      }
    }

    function testVideoDisplay() {
      log('🧪 测试视频显示方式...');

      const remoteVideo = document.getElementById('remoteVideo');

      // 尝试不同的显示配置
      const configs = [
        { autoplay: true, muted: true, playsInline: true, controls: false },
        { autoplay: true, muted: false, playsInline: true, controls: true },
        { autoplay: false, muted: false, playsInline: true, controls: true }
      ];

      let configIndex = 0;

      function tryNextConfig() {
        if (configIndex >= configs.length) {
          log('🏁 所有配置都已尝试');
          return;
        }

        const config = configs[configIndex];
        log(`🔧 尝试配置 ${configIndex + 1}: ${JSON.stringify(config)}`);

        // 应用配置
        Object.assign(remoteVideo, config);

        // 尝试播放
        remoteVideo.play().then(() => {
          log(`✅ 配置 ${configIndex + 1} 播放成功！`);
          updateStatus(`配置 ${configIndex + 1} 成功`, 'success');
        }).catch(e => {
          log(`❌ 配置 ${configIndex + 1} 失败: ${e.message}`);
          configIndex++;
          setTimeout(tryNextConfig, 1000);
        });
      }

      if (remoteVideo.srcObject) {
        tryNextConfig();
      } else {
        log('❌ 没有远程视频流可测试');
      }
    }

    function checkMediaSending() {
      log('🔍 检查媒体发送状态...');

      // 检查本地流
      if (localStream) {
        log('📹 本地流状态:');
        const videoTracks = localStream.getVideoTracks();
        videoTracks.forEach((track, index) => {
          log(`  视频轨道 ${index}: enabled=${track.enabled}, readyState=${track.readyState}, muted=${track.muted}`);

          // 获取轨道设置
          const settings = track.getSettings();
          log(`  设置: ${JSON.stringify(settings)}`);

          // 获取轨道约束
          const constraints = track.getConstraints();
          log(`  约束: ${JSON.stringify(constraints)}`);
        });
      } else {
        log('❌ 没有本地流');
        return;
      }

      // 检查 WebRTC 连接统计
      if (videoroom && videoroom.webrtcStuff && videoroom.webrtcStuff.pc) {
        const pc = videoroom.webrtcStuff.pc;
        log('📊 检查 WebRTC 统计信息...');

        pc.getStats().then(stats => {
          let outboundVideoStats = null;
          let inboundVideoStats = null;

          stats.forEach(report => {
            if (report.type === 'outbound-rtp' && report.mediaType === 'video') {
              outboundVideoStats = report;
            } else if (report.type === 'inbound-rtp' && report.mediaType === 'video') {
              inboundVideoStats = report;
            }
          });

          if (outboundVideoStats) {
            log('📤 发送视频统计:');
            log(`  - 发送包数: ${outboundVideoStats.packetsSent || 0}`);
            log(`  - 发送字节: ${outboundVideoStats.bytesSent || 0}`);
            log(`  - 帧率: ${outboundVideoStats.framesPerSecond || 0} fps`);
            log(`  - 编码器: ${outboundVideoStats.encoderImplementation || 'unknown'}`);
            log(`  - 比特率: ${Math.round((outboundVideoStats.bytesSent * 8) / 1000)} kbps`);

            if (outboundVideoStats.packetsSent > 0) {
              log('✅ 正在发送视频数据！');
            } else {
              log('❌ 没有发送视频数据');
            }
          } else {
            log('❌ 没有找到发送视频统计');
          }

          if (inboundVideoStats) {
            log('📥 接收视频统计:');
            log(`  - 接收包数: ${inboundVideoStats.packetsReceived || 0}`);
            log(`  - 接收字节: ${inboundVideoStats.bytesReceived || 0}`);
            log(`  - 帧率: ${inboundVideoStats.framesPerSecond || 0} fps`);

            if (inboundVideoStats.packetsReceived > 0) {
              log('✅ 正在接收视频数据！');
            } else {
              log('❌ 没有接收到视频数据');
            }
          } else {
            log('⚠️  没有找到接收视频统计');
          }
        }).catch(e => {
          log(`❌ 获取统计信息失败: ${e.message}`);
        });
      } else {
        log('❌ 没有 WebRTC 连接');
      }
    }

    async function startTest() {
      try {
        // 检查依赖
        if (typeof adapter === 'undefined') {
          updateStatus('adapter.js未加载！', 'error');
          return;
        }
        if (typeof Janus === 'undefined') {
          updateStatus('janus.js未加载！', 'error');
          return;
        }

        log('✓ adapter.js已加载');
        log('✓ janus.js已加载');

        updateStatus('正在获取摄像头...', 'info');
        
        localStream = await navigator.mediaDevices.getUserMedia({
          video: true,
          audio: false  // 只要视频，不要音频
        });
        
        document.getElementById('localVideo').srcObject = localStream;
        updateStatus('摄像头已启动', 'success');
        
        // 初始化Janus
        initJanus();
        
      } catch (error) {
        updateStatus(`错误: ${error.message}`, 'error');
      }
    }

    function initJanus() {
      updateStatus('初始化Janus...', 'info');
      
      Janus.init({
        debug: "all",
        callback: function() {
          log('Janus初始化成功');
          
          janus = new Janus({
            server: "ws://*************:8188/",
            // 使用多个公网 STUN/TURN 服务器
            iceServers: [
              { urls: "stun:stun.l.google.com:19302" },
              { urls: "stun:stun1.l.google.com:19302" },
              { urls: "stun:stun.cloudflare.com:3478" },
              { urls: "turn:*************:3478", username: "hhzt", credential: "hhzt20130403" }
            ],
            iceTransportPolicy: "all",  // 允许所有连接方式
            // 允许所有连接类型，不强制TURN
            // iceTransportPolicy: "relay",
            success: function() {
              updateStatus('Janus连接成功', 'success');
              attachVideoRoom();
            },
            error: function(error) {
              updateStatus(`Janus连接失败: ${error}`, 'error');
            }
          });
        },
        error: function(error) {
          updateStatus(`Janus初始化失败: ${error}`, 'error');
        }
      });
    }

    function createAndJoinRoom() {
      log(`🏠 尝试创建房间 ${ROOM_ID}...`);

      const create = {
        request: "create",
        room: ROOM_ID,
        description: "测试房间 - 自动创建",
        publishers: 10,
        bitrate: 256000,
        fir_freq: 10,
        audiocodec: "opus",
        videocodec: "vp8",
        record: false,
        rec_dir: "/tmp",
        // 添加更多配置确保房间创建成功
        is_private: false,
        secret: "",
        pin: "",
        require_pvtid: false,
        signed_tokens: false
      };

      videoroom.send({ message: create });
    }

    function joinRoom(pin = null) {
      log(`尝试加入房间 ${ROOM_ID}...`);

      const register = {
        request: "join",
        room: ROOM_ID,
        ptype: "publisher",
        display: "测试用户"
      };

      // 如果提供了PIN码，添加到请求中
      if (pin !== null && pin !== undefined) {
        register.pin = pin;
        log(`使用PIN码: "${pin}"`);
      }

      // 添加调试信息
      log(`完整请求: ${JSON.stringify(register)}`);

      videoroom.send({ message: register });
      log('发送加入房间请求');
    }

    function attachVideoRoom() {
      updateStatus('连接VideoRoom...', 'info');
      
      janus.attach({
        plugin: "janus.plugin.videoroom",
        success: function(pluginHandle) {
          videoroom = pluginHandle;
          updateStatus('VideoRoom连接成功', 'success');
          
          // 直接加入房间，使用空PIN（因为Janus要求PIN字段存在）
          joinRoom("");
        },
        error: function(error) {
          updateStatus(`VideoRoom连接失败: ${error}`, 'error');
        },
        iceState: function(state) {
          log(`ICE状态: ${state}`);
          if (state === 'connected') {
            updateStatus('ICE连接成功！', 'success');
          } else if (state === 'failed') {
            updateStatus('ICE连接失败', 'error');
          }
        },
        webrtcState: function(on) {
          log(`Publisher WebRTC: ${on ? '已连接' : '已断开'}`);
          if (on) {
            updateStatus('Publisher WebRTC连接成功！', 'success');
            // 检查发送状态
            setTimeout(() => {
              if (videoroom && videoroom.webrtcStuff && videoroom.webrtcStuff.pc) {
                videoroom.webrtcStuff.pc.getSenders().forEach(sender => {
                  if (sender.track) {
                    log(`发送轨道: ${sender.track.kind}, enabled: ${sender.track.enabled}`);
                  }
                });
              }
            }, 1000);
          }
        },
        onmessage: function(msg, jsep) {
          log("收到消息: " + JSON.stringify(msg));

          // 处理房间列表响应
          if (msg["videoroom"] === "success" && msg["list"]) {
            log(`📋 找到 ${msg.list.length} 个房间:`);
            msg.list.forEach(room => {
              log(`  - 房间 ${room.room}: ${room.description} (${room.num_participants} 人)`);
            });
            return;
          }

          // 处理房间创建响应
          if (msg["videoroom"] === "created") {
            log(`✅ 房间 ${msg["room"]} 创建成功`);
            updateStatus(`房间 ${msg["room"]} 创建成功`, 'success');
            joinRoom();
            return;
          }

          // 处理错误消息
          if (msg["error"]) {
            log(`❌ 服务器错误: ${msg["error"]} (${msg["error_code"]})`);

            if (msg["error_code"] === 426) {
              // 房间不存在
              log(`🏠 房间 ${ROOM_ID} 不存在！`);
              updateStatus(`房间 ${ROOM_ID} 不存在，请点击"通过Admin API创建房间"按钮创建`, 'error');
              log(`💡 提示：点击"通过Admin API创建房间"按钮来创建房间`);
            } else if (msg["error_code"] === 429) {
              // 需要PIN码
              log(`🔐 房间 ${ROOM_ID} 需要PIN码`);

              if (window.tryingCommonPin) {
                // 如果正在尝试常见PIN码，继续下一个
                log(`❌ PIN码错误，尝试下一个...`);
                // 这里需要触发下一个PIN码尝试，但由于异步性质，我们简化处理
                window.tryingCommonPin = false;
              } else {
                updateStatus('房间需要PIN码，请在上方输入框中输入PIN码或点击"尝试常见PIN"', 'error');
                log(`💡 提示：在PIN码输入框中输入PIN码，或点击"尝试常见PIN"按钮`);
              }
            } else {
              updateStatus(`错误: ${msg["error"]}`, 'error');
            }
            return;
          }

          if (msg["videoroom"] === "joined") {
            log(`已加入房间，ID: ${msg["id"]}`);
            updateStatus(`已加入房间，开始发布流...`, 'success');

            // 发布本地流
            videoroom.createOffer({
              media: {
                audioRecv: false,
                videoRecv: false,
                audioSend: false,  // 不发送音频
                videoSend: true,   // 只发送视频
                // 确保正确的媒体方向
                data: false
              },
              stream: localStream,
              success: function(jsep) {
                log("创建offer成功");
                log(`Publisher SDP: ${jsep.sdp.substring(0, 200)}...`);
                const publish = {
                  request: "configure",
                  audio: false,  // 不配置音频
                  video: true,   // 只配置视频
                  // 添加比特率限制
                  bitrate: 256000
                };
                videoroom.send({ message: publish, jsep: jsep });
              },
              error: function(error) {
                log(`创建offer失败: ${error}`);
                updateStatus(`发布失败: ${error}`, 'error');
              }
            });
            
            // 订阅其他用户（只订阅第一个）
            if (msg["publishers"]) {
              for (let pub of msg["publishers"]) {
                log(`发现发布者: ${pub.display} (${pub.id})`);
                if (!subscribedFeeds.has(pub.id)) {
                  // 延迟订阅，确保发布者连接稳定
                  setTimeout(() => {
                    subscribeToFeed(pub.id, pub.display);
                  }, 1000);
                  break; // 只订阅第一个
                }
              }
            }
          }
          
          if (msg["videoroom"] === "event" && msg["publishers"]) {
            for (let pub of msg["publishers"]) {
              log(`新发布者: ${pub.display} (${pub.id})`);
              if (!subscribedFeeds.has(pub.id)) {
                subscribeToFeed(pub.id, pub.display);
                break; // 只订阅第一个新发布者
              }
            }
          }
          
          // 处理配置响应
          if (msg["videoroom"] === "event" && msg["configured"]) {
            log(`发布配置成功: ${JSON.stringify(msg)}`);
            updateStatus('本地流发布成功！', 'success');
          }

          if (jsep) {
            log(`处理SDP: ${jsep.type}`);
            if (jsep.type === "answer") {
              log(`Publisher收到answer SDP: ${jsep.sdp.substring(0, 200)}...`);
            }
            videoroom.handleRemoteJsep({ jsep: jsep });
          }
        },
        onlocalstream: function(stream) {
          log("本地流准备完成");
          log(`本地流轨道数量: ${stream.getTracks().length}`);
          stream.getTracks().forEach(track => {
            log(`本地轨道: ${track.kind}, enabled: ${track.enabled}`);
          });
        },
        onremotestream: function(stream) {
          log("收到远程流！这不应该在Publisher中发生");
          // Publisher通常不应该收到远程流
        }
      });
    }

    function subscribeToFeed(id, display) {
      // 严格检查是否已经订阅
      if (subscribedFeeds.has(id)) {
        log(`⚠️ 已经订阅过 ${display} (ID: ${id})，跳过重复订阅`);
        return;
      }

      log(`🔄 开始订阅 ${display} (ID: ${id}) 的流...`);
      log(`📊 当前已订阅列表: [${Array.from(subscribedFeeds).join(', ')}]`);
      subscribedFeeds.add(id); // 标记为已订阅

      let remoteFeed = null; // 在外部声明变量

      janus.attach({
        plugin: "janus.plugin.videoroom",
        success: function(pluginHandle) {
          remoteFeed = pluginHandle; // 赋值给外部变量
          log(`✅ 订阅插件连接成功`);

          // 使用时间戳和随机数确保唯一性
          const uniquePrivateId = Date.now() + Math.floor(Math.random() * 1000000);
          const subscribe = {
            request: "join",
            room: ROOM_ID,
            ptype: "subscriber",
            feed: id,
            private_id: uniquePrivateId,
            offer_audio: false,  // 不订阅音频
            offer_video: true,   // 只订阅视频
            pin: "", // 添加空PIN，因为Janus要求PIN字段存在
            // 只配置视频流 - 使用正确的MID
            streams: [
              { feed: id, mid: "0", send: true }  // 视频流使用 mid: "0"
            ]
          };
          log(`🆔 使用唯一订阅ID: ${uniquePrivateId}`);

          log(`📤 发送订阅请求: ${JSON.stringify(subscribe)}`);
          remoteFeed.send({ message: subscribe });
        },
        error: function(error) {
          log(`订阅失败: ${error}`);
        },
        iceState: function(state) {
          log(`📡 订阅ICE状态: ${state}`);
          if (state === 'connected') {
            log(`✅ 订阅 ${display} ICE连接成功`);
          } else if (state === 'failed') {
            log(`❌ 订阅 ${display} ICE连接失败`);
          }
        },
        webrtcState: function(on) {
          log(`📺 订阅 ${display} WebRTC: ${on ? '已连接' : '已断开'}`);
          if (on) {
            log(`✅ 订阅 ${display} WebRTC连接成功！`);
            updateStatus(`订阅 ${display} 成功，正在接收视频流`, 'success');

            // 设置连接保持机制
            if (remoteFeed && remoteFeed.webrtcStuff && remoteFeed.webrtcStuff.pc) {
              const pc = remoteFeed.webrtcStuff.pc;

              // 监听连接状态变化
              pc.onconnectionstatechange = () => {
                log(`🔗 连接状态变化: ${pc.connectionState}`);
                if (pc.connectionState === 'failed' || pc.connectionState === 'disconnected') {
                  log(`🔄 检测到连接问题，尝试重新连接...`);
                  // 不立即清理，给重连机会
                }
              };

              // 监听ICE连接状态
              pc.oniceconnectionstatechange = () => {
                log(`🧊 ICE连接状态: ${pc.iceConnectionState}`);
              };
            }
          } else {
            log(`🔍 WebRTC断开 - 这可能是正常的信令流程`);
            log(`💡 保持媒体流连接，不进行清理`);
            // 关键修复：完全阻止清理，让媒体流继续
            return false;
          }
        },
        onremotetrack: function(track, mid, on) {
          log(`🎬 收到远程轨道: ${track.kind}, mid: ${mid}, on: ${on}`);
          log(`🔍 轨道详情: enabled=${track.enabled}, muted=${track.muted}, readyState=${track.readyState}`);

          // 🔧 关键修复：不管轨道状态如何，都直接处理
          const remoteVideo = document.getElementById('remoteVideo');
          if (remoteVideo) {
            // 获取或创建远程流
            let remoteStream = remoteVideo.srcObject;
            if (!remoteStream) {
              remoteStream = new MediaStream();
              remoteVideo.srcObject = remoteStream;
              savedRemoteStream = remoteStream; // 保存引用
              log(`📺 创建新的远程流并保存引用`);
            }

            // 只处理视频轨道
            if (track.kind === 'video') {
              // 检查视频轨道是否已存在
              const existingTracks = remoteStream.getTracks().filter(t => t.kind === 'video');
              if (existingTracks.length === 0) {
                // 🔧 强制添加视频轨道
                remoteStream.addTrack(track);
                log(`📹 强制添加视频轨道到远程流 (仅视频模式)`);
              } else {
                log(`⚠️ 视频轨道已存在，跳过添加`);
              }

              // 立即尝试播放视频
              if (track.kind === 'video') {
                // 确保视频元素属性正确
                remoteVideo.autoplay = true;
                remoteVideo.playsInline = true;
                remoteVideo.controls = false;
                remoteVideo.muted = false; // 确保不静音

                // 多次尝试播放
                let playAttempts = 0;
                const tryPlay = () => {
                  playAttempts++;
                  remoteVideo.play().then(() => {
                    log(`🎉 视频播放成功！(尝试 ${playAttempts})`);
                    log(`✅ 正在显示 ${display} 的视频流`);
                    updateStatus(`✅ 成功！正在显示 ${display} 的视频`, 'success');
                  }).catch(e => {
                    log(`❌ 播放尝试 ${playAttempts} 失败: ${e.message}`);
                    if (playAttempts < 3) {
                      setTimeout(tryPlay, 500);
                    } else {
                      log(`💡 提示：点击"手动播放远程视频"按钮`);
                    }
                  });
                };

                setTimeout(tryPlay, 100);
              }
            } else if (track.kind === 'audio') {
              log(`🔇 跳过音频轨道 (仅视频模式)`);
              return; // 不处理音频轨道
            } else {
              log(`⚠️ ${track.kind}轨道已存在，跳过添加`);
            }
          } else {
            log(`❌ 找不到remoteVideo元素`);
          }

          // 只监听视频轨道状态变化
          if (track.kind === 'video') {
            track.onmute = () => {
              log(`🔇 视频轨道被静音 - 但已添加到流中`);
            };
            track.onunmute = () => {
              log(`🔊 视频轨道取消静音`);
              // 当视频轨道取消静音时，强制尝试播放
              const remoteVideo = document.getElementById('remoteVideo');
              if (remoteVideo && remoteVideo.srcObject) {
                // 确保视频元素配置正确
                remoteVideo.autoplay = true;
                remoteVideo.playsInline = true;
                remoteVideo.muted = false;

                // 立即尝试播放
                setTimeout(() => {
                  remoteVideo.play().then(() => {
                    log(`🎉 视频轨道取消静音后播放成功！`);
                    updateStatus(`✅ 远程视频正在播放`, 'success');
                  }).catch(e => {
                    log(`❌ 取消静音后播放失败: ${e.message}`);
                    log(`💡 请点击"手动播放远程视频"按钮`);
                  });
                }, 50);
              }
            };
            track.onended = () => {
              log(`⏹️ 视频轨道已结束`);
            };
          }
        },
        onmessage: function(msg, jsep) {
          log(`📨 订阅消息: ${JSON.stringify(msg)}`);

          // 详细记录所有消息类型
          if (msg["videoroom"]) {
            log(`📋 VideoRoom事件: ${msg["videoroom"]}`);
          }
          if (msg["error"]) {
            log(`❌ 服务器错误: ${msg["error"]} - ${msg["error_code"]}`);
            // 如果是服务器错误，可能需要重试
            if (msg["error_code"] === 426) {
              log(`🔄 检测到会话冲突，尝试重新订阅...`);
              setTimeout(() => {
                subscribedFeeds.delete(id);
                subscribeToFeed(id, display);
              }, 2000);
            } else if (msg["error_code"] === 429) {
              log(`🔐 订阅也需要PIN码，这个问题已在代码中修复`);
              log(`💡 如果看到这条消息，说明修复没有生效，请刷新页面`);
            }
          }

          if (msg["videoroom"] === "attached") {
            log(`✅ 订阅 ${display} 成功`);
          }

          if (msg["videoroom"] === "event") {
            if (msg["started"]) {
              log(`🚀 订阅流开始: ${msg["started"]}`);
            }
            if (msg["error"]) {
              log(`❌ 订阅错误: ${msg["error"]}`);
            }
          }

          if (jsep) {
            log(`📥 处理订阅SDP: ${jsep.type}`);

            try {
              log(`📥 收到offer SDP: ${jsep.sdp.substring(0, 200)}...`);

              // 直接调用createAnswer，不使用setTimeout
              remoteFeed.createAnswer({
                jsep: jsep,
                media: {
                  audioSend: false,
                  videoSend: true,
                  audioRecv: false,  // 不接收音频
                  videoRecv: true,   // 只接收视频
                  data: false
                },
				trickle: true,
                success: function(answerJsep) {
                  log(`✅ 订阅应答创建成功`);
                  log(`📤 Answer SDP: ${answerJsep.sdp.substring(0, 200)}...`);
                  const body = { request: "start", room: ROOM_ID };
                  remoteFeed.send({ message: body, jsep: answerJsep });
                  log(`📤 发送start请求和应答SDP`);
                },
                error: function(error) {
                  log(`❌ 创建应答失败: ${error}`);
                  log(`错误详情: ${JSON.stringify(error)}`);
                  console.error('createAnswer error:', error);
                }
              });
            } catch (e) {
              log(`❌ createAnswer异常: ${e.message}`);
              console.error('createAnswer exception:', e);
            }
          }
        },
        iceState: function(state) {
          log(`订阅ICE状态: ${state}`);
          if (state === 'connected') {
            log(`订阅 ${display} ICE连接成功`);
            // 连接成功后立即检查连接质量
            setTimeout(() => {
              if (remoteFeed && remoteFeed.webrtcStuff && remoteFeed.webrtcStuff.pc) {
                remoteFeed.webrtcStuff.pc.getStats().then(stats => {
                  stats.forEach(report => {
                    if (report.type === 'candidate-pair' && report.state === 'succeeded') {
                      log(`🔗 活跃连接对: ${report.localCandidateId} -> ${report.remoteCandidateId}`);
                    }
                  });
                }).catch(e => log(`📊 获取统计信息失败: ${e.message}`));
              }
            }, 1000);
          } else if (state === 'failed') {
            log(`订阅 ${display} ICE连接失败`);
          }
        },
        webrtcState: function(on) {
          log(`订阅 ${display} WebRTC: ${on ? '已连接' : '已断开'}`);
          if (on) {
            updateStatus(`订阅 ${display} WebRTC连接成功！`, 'success');
            // 检查接收状态
            setTimeout(() => {
              if (remoteFeed && remoteFeed.webrtcStuff && remoteFeed.webrtcStuff.pc) {
                remoteFeed.webrtcStuff.pc.getReceivers().forEach(receiver => {
                  if (receiver.track) {
                    log(`接收轨道: ${receiver.track.kind}, enabled: ${receiver.track.enabled}, muted: ${receiver.track.muted}`);
                  }
                });

                // 检查连接统计信息
                remoteFeed.webrtcStuff.pc.getStats().then(stats => {
                  stats.forEach(report => {
                    if (report.type === 'inbound-rtp' && report.mediaType === 'video') {
                      log(`📊 视频接收统计: 包数=${report.packetsReceived}, 字节数=${report.bytesReceived}`);
                    }
                    if (report.type === 'candidate-pair' && report.state === 'succeeded') {
                      log(`🔗 成功的候选对: ${report.localCandidateId} -> ${report.remoteCandidateId}`);
                      log(`📊 RTT: ${report.currentRoundTripTime}ms, 发送: ${report.bytesSent}, 接收: ${report.bytesReceived}`);
                    }
                  });
                }).catch(e => log(`📊 获取统计信息失败: ${e.message}`));
              }
            }, 1000);
          } else {
            log(`🔍 WebRTC断开 - 保持媒体流连接`);
            log(`💡 不进行清理，继续播放视频`);
            // 关键修复：完全阻止清理
            return false;
          }
        },

        hangup: function(reason) {
          log(`📞 订阅连接挂断 - 原因: ${reason || '未知'}`);
        },

        detached: function() {
          log(`🔌 订阅插件已分离`);
        },

        oncleanup: function() {
          log(`🚫 阻止清理 ${display} 的连接 - 保持视频流`);
          // 不清理远程视频，保持流连接
          log(`💡 视频流将继续播放，不进行任何清理操作`);
          // 不从已订阅列表中移除，保持订阅状态
          // subscribedFeeds.delete(id);
        }
      });
    }

    window.onload = function() {
      updateStatus('页面已加载，点击开始测试', 'info');
    };
  </script>
</body>
</html>
