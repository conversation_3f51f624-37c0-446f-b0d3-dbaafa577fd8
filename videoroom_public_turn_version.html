<!DOCTYPE html>
<html>
<head>
  <meta charset="UTF-8">
  <title>VideoRoom - 公共TURN服务器版本</title>
  <style>
    body { font-family: Arial, sans-serif; margin: 20px; }
    .video-container { display: flex; gap: 20px; margin: 20px 0; }
    video { width: 320px; height: 240px; border: 1px solid #ccc; }
    .controls { margin: 20px 0; }
    button { padding: 10px 20px; margin: 5px; }
    .status { margin: 10px 0; padding: 10px; border-radius: 3px; }
    .success { background-color: #d4edda; color: #155724; }
    .error { background-color: #f8d7da; color: #721c24; }
    .warning { background-color: #fff3cd; color: #856404; }
    .info { background-color: #d1ecf1; color: #0c5460; }
    #log { height: 300px; overflow-y: auto; border: 1px solid #ccc; padding: 10px; background: #f9f9f9; font-family: monospace; font-size: 12px; }
  </style>
</head>
<body>
  <h1>VideoRoom测试 - 公共TURN服务器版本</h1>
  
  <div class="controls">
    <button onclick="startVideo()">开始视频</button>
    <button onclick="joinRoom()">加入房间</button>
    <button onclick="leaveRoom()">离开房间</button>
    <button onclick="clearLog()">清空日志</button>
  </div>

  <div id="status" class="status info">准备就绪</div>

  <div class="video-container">
    <div>
      <h3>本地视频</h3>
      <video id="localVideo" autoplay muted></video>
    </div>
    <div>
      <h3>远程视频</h3>
      <video id="remoteVideo" autoplay></video>
    </div>
  </div>

  <div>
    <h3>详细日志</h3>
    <div id="log"></div>
  </div>

  <script>
    let janus = null;
    let videoroom = null;
    let localStream = null;
    let remoteStream = null;
    let myid = null;
    let myroom = 1234;

    function log(message, type = 'info') {
      const logDiv = document.getElementById('log');
      const timestamp = new Date().toLocaleTimeString();
      const className = type === 'error' ? 'error' : type === 'warning' ? 'warning' : type === 'success' ? 'success' : 'info';
      logDiv.innerHTML += `<div class="${className}">[${timestamp}] ${message}</div>`;
      logDiv.scrollTop = logDiv.scrollHeight;
      console.log(message);
    }

    function updateStatus(message, type = 'info') {
      const statusDiv = document.getElementById('status');
      statusDiv.className = `status ${type}`;
      statusDiv.textContent = message;
      log(message, type);
    }

    function clearLog() {
      document.getElementById('log').innerHTML = '';
    }

    async function startVideo() {
      try {
        updateStatus('正在获取摄像头权限...', 'info');
        
        localStream = await navigator.mediaDevices.getUserMedia({
          video: { width: 320, height: 240 },
          audio: true
        });
        
        document.getElementById('localVideo').srcObject = localStream;
        updateStatus('摄像头已启动', 'success');
        
        // 初始化Janus
        initJanus();
        
      } catch (error) {
        updateStatus(`获取摄像头失败: ${error.message}`, 'error');
      }
    }

    function initJanus() {
      updateStatus('正在连接Janus服务器...', 'info');
      
      // 使用公共TURN服务器的配置
      const iceServers = [
        // 多个公共STUN服务器
        { urls: "stun:stun.l.google.com:19302" },
        { urls: "stun:stun1.l.google.com:19302" },
        { urls: "stun:stun2.l.google.com:19302" },
        { urls: "stun:stun.services.mozilla.com:3478" },
        // 公共TURN服务器（免费但有限制）
        { 
          urls: "turn:openrelay.metered.ca:80",
          username: "openrelayproject",
          credential: "openrelayproject"
        },
        { 
          urls: "turn:openrelay.metered.ca:443",
          username: "openrelayproject", 
          credential: "openrelayproject"
        },
        { 
          urls: "turn:openrelay.metered.ca:443?transport=tcp",
          username: "openrelayproject",
          credential: "openrelayproject"
        },
        // 自建TURN服务器（作为备选）
        { urls: "turn:60.255.197.32:3478", username: "hhzt", credential: "hhzt20130403" }
      ];

      Janus.init({
        debug: "all",
        callback: function() {
          janus = new Janus({
            server: "ws://60.255.197.32:8188/",
            iceServers: iceServers,
            iceCandidatePoolSize: 10,
            success: function() {
              updateStatus('Janus连接成功', 'success');
              attachVideoRoom();
            },
            error: function(error) {
              updateStatus(`Janus连接失败: ${error}`, 'error');
            },
            destroyed: function() {
              updateStatus('Janus连接已断开', 'warning');
            }
          });
        }
      });
    }

    function attachVideoRoom() {
      updateStatus('正在连接VideoRoom插件...', 'info');
      
      janus.attach({
        plugin: "janus.plugin.videoroom",
        opaqueId: "videoroomtest-" + Janus.randomString(12),
        success: function(pluginHandle) {
          videoroom = pluginHandle;
          updateStatus('VideoRoom插件连接成功', 'success');
          log("VideoRoom插件句柄: " + videoroom.getPlugin() + " " + videoroom.getId());
        },
        error: function(error) {
          updateStatus(`VideoRoom插件连接失败: ${error}`, 'error');
        },
        iceState: function(state) {
          log(`ICE状态: ${state}`);
          if (state === 'connected') {
            updateStatus('ICE连接已建立', 'success');
          } else if (state === 'failed') {
            updateStatus('ICE连接失败', 'error');
          }
        },
        mediaState: function(medium, on) {
          log(`媒体状态变化: ${medium} ${on ? '开启' : '关闭'}`);
        },
        webrtcState: function(on) {
          log(`WebRTC状态: ${on ? '已连接' : '已断开'}`);
        },
        onmessage: function(msg, jsep) {
          log("收到消息: " + JSON.stringify(msg));
          
          const event = msg["videoroom"];
          if (event) {
            if (event === "joined") {
              myid = msg["id"];
              updateStatus(`已加入房间，ID: ${myid}`, 'success');
              
              if (msg["publishers"]) {
                const list = msg["publishers"];
                for (let f in list) {
                  const id = list[f]["id"];
                  const display = list[f]["display"];
                  log(`发现发布者: ${display} (${id})`);
                  subscribeToFeed(id, display);
                }
              }
            } else if (event === "event") {
              if (msg["publishers"]) {
                const list = msg["publishers"];
                for (let f in list) {
                  const id = list[f]["id"];
                  const display = list[f]["display"];
                  log(`新发布者加入: ${display} (${id})`);
                  subscribeToFeed(id, display);
                }
              }
            }
          }
          
          if (jsep) {
            log("处理SDP: " + jsep.type);
            videoroom.handleRemoteJsep({ jsep: jsep });
          }
        },
        onlocalstream: function(stream) {
          log("本地流已准备");
          // 本地流已经在startVideo中设置
        },
        onremotestream: function(stream) {
          log("收到远程流");
          document.getElementById('remoteVideo').srcObject = stream;
          updateStatus('远程视频流已连接', 'success');
        },
        oncleanup: function() {
          log("清理WebRTC连接");
        }
      });
    }

    function joinRoom() {
      if (!videoroom) {
        updateStatus('请先启动视频', 'warning');
        return;
      }
      
      updateStatus('正在加入房间...', 'info');
      
      const register = {
        request: "join",
        room: myroom,
        ptype: "publisher",
        display: "用户_" + Janus.randomString(4)
      };
      
      videoroom.send({ message: register });
    }

    function publishOwnFeed() {
      videoroom.createOffer({
        media: { audioRecv: false, videoRecv: false, audioSend: true, videoSend: true },
        stream: localStream,
        success: function(jsep) {
          log("发布本地流");
          const publish = { request: "configure", audio: true, video: true };
          videoroom.send({ message: publish, jsep: jsep });
        },
        error: function(error) {
          updateStatus(`发布失败: ${error}`, 'error');
        }
      });
    }

    function subscribeToFeed(id, display) {
      log(`订阅用户 ${display} 的流`);
      
      janus.attach({
        plugin: "janus.plugin.videoroom",
        opaqueId: "videoroomlistener-" + Janus.randomString(12),
        success: function(pluginHandle) {
          const remoteFeed = pluginHandle;
          
          const subscribe = {
            request: "join",
            room: myroom,
            ptype: "subscriber",
            feed: id
          };
          
          remoteFeed.send({ message: subscribe });
        },
        error: function(error) {
          log(`订阅失败: ${error}`, 'error');
        },
        onmessage: function(msg, jsep) {
          const event = msg["videoroom"];
          if (event === "attached") {
            log(`已订阅用户 ${display}`);
          }
          
          if (jsep) {
            remoteFeed.createAnswer({
              jsep: jsep,
              media: { audioSend: false, videoSend: false },
              success: function(jsep) {
                const body = { request: "start", room: myroom };
                remoteFeed.send({ message: body, jsep: jsep });
              },
              error: function(error) {
                log(`创建应答失败: ${error}`, 'error');
              }
            });
          }
        },
        onremotestream: function(stream) {
          log(`收到 ${display} 的远程流`);
          document.getElementById('remoteVideo').srcObject = stream;
          updateStatus(`正在显示 ${display} 的视频`, 'success');
        }
      });
    }

    function leaveRoom() {
      if (videoroom) {
        const leave = { request: "leave" };
        videoroom.send({ message: leave });
        videoroom.hangup();
      }
      
      if (localStream) {
        localStream.getTracks().forEach(track => track.stop());
        localStream = null;
      }
      
      document.getElementById('localVideo').srcObject = null;
      document.getElementById('remoteVideo').srcObject = null;
      
      updateStatus('已离开房间', 'info');
    }

    // 页面加载时的提示
    window.onload = function() {
      updateStatus('请点击"开始视频"按钮开始测试', 'info');
      log('此版本使用公共TURN服务器，可能有连接限制');
      log('如果连接失败，请尝试刷新页面重试');
    };
  </script>
  
  <!-- 引入Janus库 -->
  <script src="https://janus.conf.meetecho.com/janus.js"></script>
</body>
</html>
