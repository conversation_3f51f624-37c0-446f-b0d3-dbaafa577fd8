<!DOCTYPE html>
<html>
<head>
  <title>Win10 WebRTC 兼容性测试</title>
  <style>
    body { font-family: Arial, sans-serif; margin: 20px; }
    .test-section { margin: 20px 0; padding: 15px; border: 1px solid #ccc; border-radius: 5px; }
    .success { background-color: #d4edda; }
    .error { background-color: #f8d7da; }
    .warning { background-color: #fff3cd; }
    button { padding: 10px 20px; margin: 5px; }
    video { width: 320px; height: 240px; border: 2px solid #000; margin: 10px; }
    #results { height: 400px; overflow-y: scroll; border: 1px solid #ccc; padding: 10px; font-family: monospace; font-size: 12px; }
  </style>
</head>
<body>
  <h1>Win10 WebRTC 兼容性测试</h1>
  <p><strong>专门针对 Windows 10 系统的 WebRTC 问题诊断</strong></p>
  
  <div class="test-section">
    <h3>系统信息</h3>
    <div id="systemInfo"></div>
  </div>
  
  <div class="test-section">
    <h3>测试控制</h3>
    <button onclick="runWin10Tests()">运行 Win10 专项测试</button>
    <button onclick="testWebRTCReceive()">测试接收功能</button>
    <button onclick="clearResults()">清除结果</button>
  </div>
  
  <div class="test-section">
    <h3>视频测试</h3>
    <video id="localVideo" autoplay muted playsinline></video>
    <video id="remoteVideo" autoplay playsinline></video>
  </div>
  
  <div class="test-section">
    <h3>测试结果</h3>
    <div id="results"></div>
  </div>

  <script src="adapter-latest.js"></script>
  <script src="janus.js"></script>
  
  <script>
    let janus = null;
    let remoteFeed = null;
    
    function log(message, type = 'info') {
      const timestamp = new Date().toLocaleTimeString();
      const results = document.getElementById('results');
      const color = type === 'error' ? 'red' : type === 'success' ? 'green' : type === 'warning' ? 'orange' : 'black';
      results.innerHTML += `<span style="color: ${color}">[${timestamp}] ${message}</span><br>`;
      results.scrollTop = results.scrollHeight;
      console.log(message);
    }
    
    function clearResults() {
      document.getElementById('results').innerHTML = '';
    }
    
    function showSystemInfo() {
      const info = document.getElementById('systemInfo');
      const isWin10 = navigator.userAgent.includes('Windows NT 10.0');
      const browserInfo = getBrowserInfo();
      
      info.innerHTML = `
        <p><strong>操作系统:</strong> ${isWin10 ? 'Windows 10 ✅' : '非 Windows 10 ⚠️'}</p>
        <p><strong>浏览器:</strong> ${browserInfo.name} ${browserInfo.version}</p>
        <p><strong>用户代理:</strong> ${navigator.userAgent}</p>
        <p><strong>WebRTC 支持:</strong> ${window.RTCPeerConnection ? '支持' : '不支持'}</p>
        <p><strong>adapter.js 版本:</strong> ${window.adapter ? window.adapter.browserDetails.version : '未加载'}</p>
      `;
    }
    
    function getBrowserInfo() {
      const ua = navigator.userAgent;
      if (ua.includes('Chrome')) {
        const match = ua.match(/Chrome\/(\d+)/);
        return { name: 'Chrome', version: match ? match[1] : 'unknown' };
      } else if (ua.includes('Firefox')) {
        const match = ua.match(/Firefox\/(\d+)/);
        return { name: 'Firefox', version: match ? match[1] : 'unknown' };
      } else if (ua.includes('Edge')) {
        const match = ua.match(/Edge\/(\d+)/);
        return { name: 'Edge', version: match ? match[1] : 'unknown' };
      }
      return { name: 'Unknown', version: 'unknown' };
    }
    
    async function runWin10Tests() {
      log('🔍 开始 Windows 10 专项测试', 'info');
      clearResults();
      
      // 检查系统特性
      await checkWin10Features();
      
      // 检查网络权限
      await checkNetworkPermissions();
      
      // 检查 WebRTC 配置
      await checkWebRTCConfig();
      
      // 测试基础连接
      await testBasicConnection();
    }
    
    async function checkWin10Features() {
      log('📋 检查 Windows 10 特性...', 'info');
      
      // 检查是否为 Win10
      const isWin10 = navigator.userAgent.includes('Windows NT 10.0');
      log(`操作系统: ${isWin10 ? 'Windows 10' : '其他系统'}`, isWin10 ? 'success' : 'warning');
      
      // 检查浏览器版本
      const browserInfo = getBrowserInfo();
      log(`浏览器: ${browserInfo.name} ${browserInfo.version}`, 'info');
      
      // 检查 WebRTC API
      const apis = [
        'RTCPeerConnection',
        'RTCSessionDescription', 
        'RTCIceCandidate',
        'MediaStream'
      ];
      
      apis.forEach(api => {
        const supported = window[api] !== undefined;
        log(`${api}: ${supported ? '支持' : '不支持'}`, supported ? 'success' : 'error');
      });
    }
    
    async function checkNetworkPermissions() {
      log('🌐 检查网络权限...', 'info');
      
      try {
        // 测试基础网络连接
        const response = await fetch('http://*************:8088/janus/info');
        const data = await response.json();
        log('✅ HTTP 连接正常', 'success');
        log(`Janus 版本: ${data.version_string || 'unknown'}`, 'info');
      } catch (error) {
        log(`❌ HTTP 连接失败: ${error.message}`, 'error');
      }
      
      try {
        // 测试 WebSocket 连接
        const ws = new WebSocket('ws://*************:8188/');
        ws.onopen = function() {
          log('✅ WebSocket 连接正常', 'success');
          ws.close();
        };
        ws.onerror = function() {
          log('❌ WebSocket 连接失败', 'error');
        };
      } catch (error) {
        log(`❌ WebSocket 创建失败: ${error.message}`, 'error');
      }
    }
    
    async function checkWebRTCConfig() {
      log('🔧 检查 WebRTC 配置...', 'info');
      
      // 检查媒体设备权限
      try {
        const stream = await navigator.mediaDevices.getUserMedia({ video: true, audio: false });
        log('✅ 摄像头权限正常', 'success');
        
        const videoTrack = stream.getVideoTracks()[0];
        const settings = videoTrack.getSettings();
        log(`摄像头设置: ${settings.width}x${settings.height}`, 'info');
        
        document.getElementById('localVideo').srcObject = stream;
        stream.getTracks().forEach(track => track.stop());
      } catch (error) {
        log(`❌ 摄像头权限失败: ${error.message}`, 'error');
      }
      
      // 检查 ICE 候选者收集
      const pc = new RTCPeerConnection({
        iceServers: [{ urls: 'stun:stun.l.google.com:19302' }]
      });
      
      let candidateCount = 0;
      pc.onicecandidate = function(event) {
        if (event.candidate) {
          candidateCount++;
          log(`ICE 候选者 ${candidateCount}: ${event.candidate.candidate.substring(0, 50)}...`, 'info');
        } else {
          log(`✅ ICE 候选者收集完成: ${candidateCount} 个`, 'success');
          pc.close();
        }
      };
      
      try {
        const offer = await pc.createOffer();
        await pc.setLocalDescription(offer);
        log('✅ ICE 候选者收集开始', 'success');
      } catch (error) {
        log(`❌ ICE 候选者收集失败: ${error.message}`, 'error');
        pc.close();
      }
    }
    
    async function testBasicConnection() {
      log('🔗 测试基础连接...', 'info');
      
      try {
        await testWebRTCReceive();
      } catch (error) {
        log(`❌ 连接测试失败: ${error.message}`, 'error');
      }
    }
    
    async function testWebRTCReceive() {
      log('📥 测试 WebRTC 接收功能...', 'info');
      
      Janus.init({
        debug: "all",
        callback: function() {
          log('✅ Janus 初始化成功', 'success');
          
          janus = new Janus({
            server: "ws://*************:8188/",
            // Win10 优化配置
            iceServers: [
              { urls: "stun:stun.l.google.com:19302" }
            ],
            iceTransportPolicy: "all",
            bundlePolicy: "max-bundle",
            rtcpMuxPolicy: "require",
            success: function() {
              log('✅ Janus 连接成功', 'success');
              queryAndSubscribe();
            },
            error: function(error) {
              log(`❌ Janus 连接失败: ${error}`, 'error');
            }
          });
        }
      });
    }
    
    function queryAndSubscribe() {
      janus.attach({
        plugin: "janus.plugin.videoroom",
        success: function(pluginHandle) {
          log('✅ VideoRoom 插件连接成功', 'success');
          
          // 查询房间参与者
          const listRequest = { request: "listparticipants", room: 1000 };
          pluginHandle.send({
            message: listRequest,
            success: function(result) {
              log(`📋 房间参与者: ${JSON.stringify(result)}`, 'info');
              
              if (result.participants && result.participants.length > 0) {
                const publisher = result.participants[0];
                log(`🎯 订阅发布者: ${publisher.display} (${publisher.id})`, 'info');
                subscribeToPublisher(publisher.id, publisher.display);
              } else {
                log('⚠️ 房间中没有发布者', 'warning');
              }
            }
          });
        },
        error: function(error) {
          log(`❌ VideoRoom 连接失败: ${error}`, 'error');
        }
      });
    }
    
    function subscribeToPublisher(id, display) {
      janus.attach({
        plugin: "janus.plugin.videoroom",
        success: function(pluginHandle) {
          remoteFeed = pluginHandle;
          log('✅ 订阅插件连接成功', 'success');
          
          const subscribe = {
            request: "join",
            room: 1000,
            ptype: "subscriber",
            feed: id,
            private_id: Math.floor(Math.random() * 1000000),
            offer_audio: false,
            offer_video: true,
            pin: ""
          };
          
          remoteFeed.send({ message: subscribe });
        },
        error: function(error) {
          log(`❌ 订阅失败: ${error}`, 'error');
        },
        iceState: function(state) {
          log(`🧊 订阅 ICE 状态: ${state}`, state === 'connected' ? 'success' : 'info');
        },
        webrtcState: function(on) {
          log(`🔗 订阅 WebRTC 状态: ${on ? '已连接' : '已断开'}`, on ? 'success' : 'error');
        },
        onremotetrack: function(track, mid, on) {
          log(`🎬 收到远程轨道: ${track.kind}, mid: ${mid}, on: ${on}`, 'success');
          
          if (track.kind === 'video' && on) {
            const remoteVideo = document.getElementById('remoteVideo');
            const stream = new MediaStream([track]);
            remoteVideo.srcObject = stream;
            
            remoteVideo.onloadedmetadata = function() {
              log(`✅ 视频元数据加载: ${remoteVideo.videoWidth}x${remoteVideo.videoHeight}`, 'success');
            };
            
            remoteVideo.onplay = function() {
              log('🎉 视频开始播放！', 'success');
            };
            
            remoteVideo.onerror = function(e) {
              log(`❌ 视频播放错误: ${e.message}`, 'error');
            };
          }
        },
        onmessage: function(msg, jsep) {
          log(`📨 订阅消息: ${JSON.stringify(msg)}`, 'info');
          
          if (jsep) {
            remoteFeed.createAnswer({
              jsep: jsep,
              media: { audioSend: false, videoSend: false },
              success: function(jsep) {
                const body = { request: "start", room: 1000 };
                remoteFeed.send({ message: body, jsep: jsep });
              },
              error: function(error) {
                log(`❌ 创建应答失败: ${error}`, 'error');
              }
            });
          }
        }
      });
    }
    
    // 页面加载时显示系统信息
    window.onload = function() {
      showSystemInfo();
      log('🔧 Win10 WebRTC 测试工具已准备就绪', 'info');
    };
  </script>
</body>
</html>
