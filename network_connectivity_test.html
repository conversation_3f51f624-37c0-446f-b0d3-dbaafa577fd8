<!DOCTYPE html>
<html>
<head>
  <meta charset="UTF-8">
  <title>网络连通性测试工具</title>
  <style>
    body { font-family: Arial, sans-serif; margin: 20px; }
    .test-section { margin: 20px 0; padding: 15px; border: 1px solid #ccc; border-radius: 5px; }
    .result { margin: 10px 0; padding: 10px; border-radius: 3px; }
    .success { background-color: #d4edda; color: #155724; }
    .error { background-color: #f8d7da; color: #721c24; }
    .warning { background-color: #fff3cd; color: #856404; }
    .info { background-color: #d1ecf1; color: #0c5460; }
    button { padding: 10px 20px; margin: 5px; }
    #log { height: 300px; overflow-y: auto; border: 1px solid #ccc; padding: 10px; background: #f9f9f9; font-family: monospace; font-size: 12px; }
    .test-item { margin: 10px 0; padding: 10px; border-left: 3px solid #007bff; background: #f8f9fa; }
  </style>
</head>
<body>
  <h1>网络连通性诊断工具</h1>
  
  <div class="test-section">
    <h3>基础连通性测试</h3>
    <button onclick="testBasicConnectivity()">开始基础测试</button>
    <button onclick="testServerPorts()">测试服务器端口</button>
    <button onclick="testSTUNServers()">测试STUN服务器</button>
    <div id="basicResults"></div>
  </div>

  <div class="test-section">
    <h3>WebSocket连接测试</h3>
    <button onclick="testJanusWebSocket()">测试Janus WebSocket</button>
    <div id="wsResults"></div>
  </div>

  <div class="test-section">
    <h3>网络环境分析</h3>
    <button onclick="analyzeNetworkEnvironment()">分析网络环境</button>
    <div id="networkAnalysis"></div>
  </div>

  <div class="test-section">
    <h3>详细日志</h3>
    <div id="log"></div>
  </div>

  <script>
    function log(message, type = 'info') {
      const logDiv = document.getElementById('log');
      const timestamp = new Date().toLocaleTimeString();
      const className = type === 'error' ? 'error' : type === 'warning' ? 'warning' : type === 'success' ? 'success' : 'info';
      logDiv.innerHTML += `<div class="${className}">[${timestamp}] ${message}</div>`;
      logDiv.scrollTop = logDiv.scrollHeight;
      console.log(message);
    }

    async function testBasicConnectivity() {
      log('开始基础连通性测试...');
      const resultsDiv = document.getElementById('basicResults');
      let results = '';

      // 测试公网连通性
      try {
        const response = await fetch('https://api.ipify.org?format=json', { 
          method: 'GET',
          mode: 'cors'
        });
        const data = await response.json();
        results += `<div class="test-item success">✓ 公网连通性正常，IP: ${data.ip}</div>`;
        log(`公网IP: ${data.ip}`, 'success');
      } catch (error) {
        results += `<div class="test-item error">✗ 公网连通性失败: ${error.message}</div>`;
        log(`公网连通性测试失败: ${error.message}`, 'error');
      }

      // 测试DNS解析
      try {
        const start = Date.now();
        await fetch('https://www.google.com/favicon.ico', { 
          method: 'HEAD',
          mode: 'no-cors'
        });
        const duration = Date.now() - start;
        results += `<div class="test-item success">✓ DNS解析正常 (${duration}ms)</div>`;
        log(`DNS解析正常，耗时: ${duration}ms`, 'success');
      } catch (error) {
        results += `<div class="test-item error">✗ DNS解析失败: ${error.message}</div>`;
        log(`DNS解析失败: ${error.message}`, 'error');
      }

      resultsDiv.innerHTML = results;
    }

    async function testServerPorts() {
      log('开始服务器端口测试...');
      const resultsDiv = document.getElementById('basicResults');
      
      const ports = [
        { port: 8188, name: 'Janus WebSocket', protocol: 'ws' },
        { port: 3478, name: 'TURN UDP', protocol: 'udp' },
        { port: 3479, name: 'TURN TCP', protocol: 'tcp' }
      ];

      let results = resultsDiv.innerHTML;

      for (const portInfo of ports) {
        try {
          if (portInfo.protocol === 'ws') {
            // 测试WebSocket连接
            const wsUrl = `ws://*************:${portInfo.port}/`;
            const ws = new WebSocket(wsUrl);
            
            await new Promise((resolve, reject) => {
              const timeout = setTimeout(() => {
                ws.close();
                reject(new Error('连接超时'));
              }, 5000);

              ws.onopen = () => {
                clearTimeout(timeout);
                ws.close();
                resolve();
              };

              ws.onerror = (error) => {
                clearTimeout(timeout);
                reject(error);
              };
            });

            results += `<div class="test-item success">✓ ${portInfo.name} (${portInfo.port}) 连接成功</div>`;
            log(`${portInfo.name} 端口 ${portInfo.port} 连接成功`, 'success');
          } else {
            // 对于UDP/TCP端口，我们使用间接方法测试
            results += `<div class="test-item warning">? ${portInfo.name} (${portInfo.port}) 需要专门工具测试</div>`;
            log(`${portInfo.name} 端口 ${portInfo.port} 需要专门的UDP/TCP测试工具`, 'warning');
          }
        } catch (error) {
          results += `<div class="test-item error">✗ ${portInfo.name} (${portInfo.port}) 连接失败: ${error.message}</div>`;
          log(`${portInfo.name} 端口 ${portInfo.port} 连接失败: ${error.message}`, 'error');
        }
      }

      resultsDiv.innerHTML = results;
    }

    async function testSTUNServers() {
      log('开始STUN服务器测试...');
      const resultsDiv = document.getElementById('basicResults');
      
      const stunServers = [
        'stun:stun.l.google.com:19302',
        'stun:stun1.l.google.com:19302',
        'stun:stun.services.mozilla.com:3478'
      ];

      let results = resultsDiv.innerHTML;

      for (const stunUrl of stunServers) {
        try {
          const pc = new RTCPeerConnection({ iceServers: [{ urls: stunUrl }] });
          
          let candidateReceived = false;
          const candidatePromise = new Promise((resolve, reject) => {
            const timeout = setTimeout(() => {
              reject(new Error('STUN服务器响应超时'));
            }, 8000);

            pc.onicecandidate = (event) => {
              if (event.candidate && event.candidate.candidate.includes('srflx')) {
                clearTimeout(timeout);
                candidateReceived = true;
                resolve(event.candidate);
              }
            };

            pc.onicegatheringstatechange = () => {
              if (pc.iceGatheringState === 'complete' && !candidateReceived) {
                clearTimeout(timeout);
                reject(new Error('未收到srflx候选者'));
              }
            };
          });

          // 创建offer触发ICE收集
          await pc.createOffer();
          
          try {
            const candidate = await candidatePromise;
            results += `<div class="test-item success">✓ ${stunUrl} 工作正常</div>`;
            log(`${stunUrl} 工作正常，收到候选者: ${candidate.candidate}`, 'success');
          } catch (error) {
            results += `<div class="test-item error">✗ ${stunUrl} 失败: ${error.message}</div>`;
            log(`${stunUrl} 测试失败: ${error.message}`, 'error');
          }

          pc.close();
        } catch (error) {
          results += `<div class="test-item error">✗ ${stunUrl} 异常: ${error.message}</div>`;
          log(`${stunUrl} 测试异常: ${error.message}`, 'error');
        }
      }

      resultsDiv.innerHTML = results;
    }

    async function testJanusWebSocket() {
      log('开始Janus WebSocket连接测试...');
      const resultsDiv = document.getElementById('wsResults');
      
      const wsUrl = 'ws://*************:8188/';
      
      try {
        const ws = new WebSocket(wsUrl);
        let connected = false;
        
        const connectionPromise = new Promise((resolve, reject) => {
          const timeout = setTimeout(() => {
            ws.close();
            reject(new Error('WebSocket连接超时'));
          }, 10000);

          ws.onopen = () => {
            clearTimeout(timeout);
            connected = true;
            log('WebSocket连接已建立', 'success');
            
            // 发送Janus协议测试消息
            const testMessage = {
              janus: "info",
              transaction: "test_" + Date.now()
            };
            
            ws.send(JSON.stringify(testMessage));
          };

          ws.onmessage = (event) => {
            try {
              const response = JSON.parse(event.data);
              log(`收到Janus响应: ${JSON.stringify(response)}`, 'success');
              
              if (response.janus === "server_info") {
                resolve(response);
              }
            } catch (error) {
              log(`解析响应失败: ${error.message}`, 'error');
            }
          };

          ws.onerror = (error) => {
            clearTimeout(timeout);
            reject(error);
          };

          ws.onclose = (event) => {
            if (!connected) {
              clearTimeout(timeout);
              reject(new Error(`WebSocket连接关闭: ${event.code} - ${event.reason}`));
            }
          };
        });

        const response = await connectionPromise;
        
        resultsDiv.innerHTML = `
          <div class="result success">
            <h4>✓ Janus WebSocket连接成功</h4>
            <p><strong>服务器信息:</strong></p>
            <pre>${JSON.stringify(response, null, 2)}</pre>
          </div>
        `;
        
        ws.close();
        
      } catch (error) {
        resultsDiv.innerHTML = `
          <div class="result error">
            <h4>✗ Janus WebSocket连接失败</h4>
            <p><strong>错误信息:</strong> ${error.message}</p>
            <p><strong>可能原因:</strong></p>
            <ul>
              <li>Janus服务器未启动</li>
              <li>防火墙阻塞端口8188</li>
              <li>网络连接问题</li>
              <li>服务器配置错误</li>
            </ul>
          </div>
        `;
        log(`Janus WebSocket连接失败: ${error.message}`, 'error');
      }
    }

    async function analyzeNetworkEnvironment() {
      log('开始网络环境分析...');
      const resultsDiv = document.getElementById('networkAnalysis');
      
      let analysis = '<div class="result info"><h4>网络环境分析</h4>';
      
      // 检测NAT类型
      try {
        const pc = new RTCPeerConnection({
          iceServers: [
            { urls: 'stun:stun.l.google.com:19302' }
          ]
        });
        
        const candidates = [];
        
        const gatheringPromise = new Promise((resolve) => {
          pc.onicecandidate = (event) => {
            if (event.candidate) {
              candidates.push(event.candidate);
            } else {
              resolve(candidates);
            }
          };
          
          setTimeout(() => resolve(candidates), 5000);
        });
        
        await pc.createOffer();
        const gatheredCandidates = await gatheringPromise;
        
        const hostCandidates = gatheredCandidates.filter(c => c.candidate.includes('host'));
        const srflxCandidates = gatheredCandidates.filter(c => c.candidate.includes('srflx'));
        
        if (hostCandidates.length === 0) {
          analysis += '<p><strong>⚠️ 网络问题:</strong> 无法获取本地网络接口信息</p>';
        } else {
          analysis += `<p><strong>✓ 本地网络:</strong> 检测到 ${hostCandidates.length} 个网络接口</p>`;
        }
        
        if (srflxCandidates.length === 0) {
          analysis += '<p><strong>⚠️ NAT问题:</strong> 无法通过STUN服务器获取公网地址</p>';
          analysis += '<p><strong>可能的NAT类型:</strong> 对称NAT或防火墙阻塞</p>';
        } else {
          analysis += `<p><strong>✓ NAT穿透:</strong> 可以通过STUN获取公网地址</p>`;
        }
        
        pc.close();
        
      } catch (error) {
        analysis += `<p><strong>✗ 网络分析失败:</strong> ${error.message}</p>`;
      }
      
      // 检测浏览器支持
      analysis += '<h5>浏览器支持检查:</h5>';
      analysis += `<p>WebRTC支持: ${!!window.RTCPeerConnection ? '✓' : '✗'}</p>`;
      analysis += `<p>WebSocket支持: ${!!window.WebSocket ? '✓' : '✗'}</p>`;
      analysis += `<p>getUserMedia支持: ${!!(navigator.mediaDevices && navigator.mediaDevices.getUserMedia) ? '✓' : '✗'}</p>`;
      
      // 网络建议
      analysis += '<h5>建议解决方案:</h5>';
      analysis += '<ol>';
      analysis += '<li>检查本地防火墙设置，确保允许WebRTC流量</li>';
      analysis += '<li>尝试使用VPN或更换网络环境</li>';
      analysis += '<li>联系网络管理员检查企业防火墙配置</li>';
      analysis += '<li>确认服务器*************的端口8188、3478、3479已开放</li>';
      analysis += '</ol>';
      
      analysis += '</div>';
      
      resultsDiv.innerHTML = analysis;
    }

    // 页面加载时自动运行基础测试
    window.onload = () => {
      setTimeout(testBasicConnectivity, 1000);
    };
  </script>
</body>
</html>
