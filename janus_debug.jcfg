# Janus 调试配置 - 用于排查 SRTP 问题
general: {
	configs_folder = "/opt/janus/etc/janus"
	plugins_folder = "/opt/janus/lib/janus/plugins"
	transports_folder = "/opt/janus/lib/janus/transports"
	events_folder = "/opt/janus/lib/janus/events"
	loggers_folder = "/opt/janus/lib/janus/loggers"
	
	debug_level = 7				# 最高调试级别
	admin_secret = "janusoverlord"
	
	# 临时禁用 WebRTC 加密进行调试
	no_webrtc_encryption = true
	
	protected_folders = [
		"/bin", "/boot", "/dev", "/etc", "/initrd", "/lib", "/lib32", "/lib64",
		"/proc", "/sbin", "/sys", "/usr", "/var",
		"/opt/janus/bin", "/opt/janus/etc", "/opt/janus/include", 
		"/opt/janus/lib", "/opt/janus/lib32", "/opt/janus/lib64", "/opt/janus/sbin"
	]
}

certificates: {
	# 使用自签名证书
}

media: {
	rtp_port_range = "20000-40000"
	min_nack_queue = 1000
	dtls_mtu = 1200
	no_media_timer = 30
	slowlink_threshold = 4
	twcc_period = 200
	dtls_timeout = 5000			# 增加 DTLS 超时
}

nat: {
	nice_debug = true			# 启用 libnice 调试
	full_trickle = true
	ice_nomination = "aggressive"
	ice_consent_freshness = true
	ice_keepalive_conncheck = true
	
	# 配置公网IP映射
	nat_1_1_mapping = "*************"
	keep_private_host = false
	
	# 强制使用指定的网络接口
	ice_enforce_list = "*************"
	ice_ignore_list = "vmnet"
	
	# TURN 服务器配置
	turn_server = "*************"
	turn_port = 3478
	turn_type = "udp"
	turn_user = "hhzt"
	turn_pwd = "hhzt20130403"
}

plugins: {
	# 启用所有插件
}

transports: {
	# 启用所有传输
}

loggers: {
	# 启用所有日志记录器
}

events: {
	# 禁用事件处理器以简化配置
}
