<!DOCTYPE html>
<html>
<head>
  <meta charset="UTF-8">
  <title>ICE连接诊断工具</title>
  <style>
    body { font-family: Arial, sans-serif; margin: 20px; }
    .test-section { margin: 20px 0; padding: 15px; border: 1px solid #ccc; border-radius: 5px; }
    .result { margin: 10px 0; padding: 10px; border-radius: 3px; }
    .success { background-color: #d4edda; color: #155724; }
    .error { background-color: #f8d7da; color: #721c24; }
    .warning { background-color: #fff3cd; color: #856404; }
    .info { background-color: #d1ecf1; color: #0c5460; }
    button { padding: 10px 20px; margin: 5px; }
    #log { height: 400px; overflow-y: auto; border: 1px solid #ccc; padding: 10px; background: #f9f9f9; font-family: monospace; font-size: 12px; }
    .candidate { margin: 5px 0; padding: 5px; background: #f0f0f0; border-left: 3px solid #007bff; }
    .candidate.host { border-left-color: #28a745; }
    .candidate.srflx { border-left-color: #ffc107; }
    .candidate.relay { border-left-color: #dc3545; }
  </style>
</head>
<body>
  <h1>ICE连接诊断工具</h1>
  
  <div class="test-section">
    <h3>当前网络信息</h3>
    <div id="networkInfo"></div>
  </div>

  <div class="test-section">
    <h3>ICE候选者收集测试</h3>
    <button onclick="testICECandidates()">开始ICE测试</button>
    <button onclick="testWithTURNOnly()">仅TURN测试</button>
    <button onclick="clearResults()">清空结果</button>
    <div id="iceResults"></div>
  </div>

  <div class="test-section">
    <h3>P2P连接测试</h3>
    <button onclick="testP2PConnection()">测试P2P连接</button>
    <div id="p2pResults"></div>
  </div>

  <div class="test-section">
    <h3>详细日志</h3>
    <div id="log"></div>
  </div>

  <script>
    let pc1, pc2;
    let candidates1 = [], candidates2 = [];

    function log(message, type = 'info') {
      const logDiv = document.getElementById('log');
      const timestamp = new Date().toLocaleTimeString();
      const className = type === 'error' ? 'error' : type === 'warning' ? 'warning' : type === 'success' ? 'success' : 'info';
      logDiv.innerHTML += `<div class="${className}">[${timestamp}] ${message}</div>`;
      logDiv.scrollTop = logDiv.scrollHeight;
      console.log(message);
    }

    function clearResults() {
      document.getElementById('iceResults').innerHTML = '';
      document.getElementById('p2pResults').innerHTML = '';
      document.getElementById('log').innerHTML = '';
      candidates1 = [];
      candidates2 = [];
    }

    async function getNetworkInfo() {
      const info = document.getElementById('networkInfo');
      try {
        // 获取公网IP
        const response = await fetch('https://api.ipify.org?format=json');
        const data = await response.json();
        
        // 获取本地IP（通过WebRTC）
        const pc = new RTCPeerConnection();
        pc.createDataChannel('test');
        
        let localIP = '未知';
        pc.onicecandidate = (event) => {
          if (event.candidate && event.candidate.candidate.includes('host')) {
            const match = event.candidate.candidate.match(/(\d+\.\d+\.\d+\.\d+)/);
            if (match) {
              localIP = match[1];
              info.innerHTML = `
                <div class="result info">
                  <strong>公网IP:</strong> ${data.ip}<br>
                  <strong>本地IP:</strong> ${localIP}<br>
                  <strong>用户代理:</strong> ${navigator.userAgent}
                </div>
              `;
            }
          }
        };
        
        await pc.createOffer();
        
        setTimeout(() => {
          pc.close();
          if (localIP === '未知') {
            info.innerHTML = `
              <div class="result info">
                <strong>公网IP:</strong> ${data.ip}<br>
                <strong>本地IP:</strong> 无法获取<br>
                <strong>用户代理:</strong> ${navigator.userAgent}
              </div>
            `;
          }
        }, 2000);
        
      } catch (error) {
        info.innerHTML = `<div class="result error">获取网络信息失败: ${error.message}</div>`;
      }
    }

    async function testICECandidates() {
      log('开始ICE候选者收集测试...');
      
      const iceServers = [
        { urls: 'stun:stun.l.google.com:19302' },
        { urls: 'stun:stun1.l.google.com:19302' },
        { urls: 'turn:*************:3478', username: 'hhzt', credential: 'hhzt20130403' },
        { urls: 'turn:*************:3479', username: 'hhzt', credential: 'hhzt20130403' }
      ];

      try {
        const pc = new RTCPeerConnection({ 
          iceServers: iceServers,
          iceCandidatePoolSize: 10
        });
        
        const candidates = [];
        let gatheringComplete = false;
        
        pc.onicecandidate = (event) => {
          if (event.candidate) {
            candidates.push(event.candidate);
            log(`收到候选者: ${event.candidate.candidate}`);
            
            // 实时更新显示
            updateCandidateDisplay(candidates);
          } else {
            gatheringComplete = true;
            log('ICE候选者收集完成');
            analyzeCandidates(candidates);
          }
        };

        pc.onicegatheringstatechange = () => {
          log(`ICE收集状态: ${pc.iceGatheringState}`);
        };

        pc.oniceconnectionstatechange = () => {
          log(`ICE连接状态: ${pc.iceConnectionState}`);
        };

        // 创建offer来触发候选者收集
        await pc.createOffer();
        
        // 等待收集完成
        setTimeout(() => {
          if (!gatheringComplete) {
            log('ICE候选者收集超时', 'warning');
            analyzeCandidates(candidates);
          }
          pc.close();
        }, 10000);
        
      } catch (error) {
        log(`ICE测试失败: ${error.message}`, 'error');
      }
    }

    async function testWithTURNOnly() {
      log('开始仅TURN服务器测试...');
      
      const iceServers = [
        { urls: 'turn:*************:3478', username: 'hhzt', credential: 'hhzt20130403' },
        { urls: 'turn:*************:3479', username: 'hhzt', credential: 'hhzt20130403' }
      ];

      try {
        const pc = new RTCPeerConnection({ 
          iceServers: iceServers,
          iceTransportPolicy: 'relay'
        });
        
        const candidates = [];
        
        pc.onicecandidate = (event) => {
          if (event.candidate) {
            candidates.push(event.candidate);
            log(`TURN候选者: ${event.candidate.candidate}`);
          } else {
            log('TURN候选者收集完成');
            
            const relayCandidates = candidates.filter(c => c.candidate.includes('relay'));
            if (relayCandidates.length > 0) {
              log(`✓ TURN服务器工作正常，收集到${relayCandidates.length}个relay候选者`, 'success');
            } else {
              log('✗ TURN服务器无法工作，未收集到relay候选者', 'error');
            }
          }
        };

        await pc.createOffer();
        
        setTimeout(() => {
          pc.close();
        }, 8000);
        
      } catch (error) {
        log(`TURN测试失败: ${error.message}`, 'error');
      }
    }

    function updateCandidateDisplay(candidates) {
      const hostCandidates = candidates.filter(c => c.candidate.includes('host'));
      const srflxCandidates = candidates.filter(c => c.candidate.includes('srflx'));
      const relayCandidates = candidates.filter(c => c.candidate.includes('relay'));
      
      const resultsDiv = document.getElementById('iceResults');
      resultsDiv.innerHTML = `
        <div class="result info">
          <h4>候选者统计:</h4>
          <div class="candidate host">Host候选者: ${hostCandidates.length}个</div>
          <div class="candidate srflx">Srflx候选者: ${srflxCandidates.length}个</div>
          <div class="candidate relay">Relay候选者: ${relayCandidates.length}个</div>
          <div><strong>总计: ${candidates.length}个</strong></div>
        </div>
      `;
    }

    function analyzeCandidates(candidates) {
      const hostCandidates = candidates.filter(c => c.candidate.includes('host'));
      const srflxCandidates = candidates.filter(c => c.candidate.includes('srflx'));
      const relayCandidates = candidates.filter(c => c.candidate.includes('relay'));
      
      let analysis = '<div class="result info"><h4>分析结果:</h4>';
      
      if (hostCandidates.length === 0) {
        analysis += '<div class="error">⚠️ 没有host候选者，可能存在网络配置问题</div>';
      } else {
        analysis += `<div class="success">✓ Host候选者正常 (${hostCandidates.length}个)</div>`;
      }
      
      if (srflxCandidates.length === 0) {
        analysis += '<div class="warning">⚠️ 没有srflx候选者，STUN服务器可能不可用</div>';
      } else {
        analysis += `<div class="success">✓ STUN服务器工作正常 (${srflxCandidates.length}个)</div>`;
      }
      
      if (relayCandidates.length === 0) {
        analysis += '<div class="error">⚠️ 没有relay候选者，TURN服务器不可用</div>';
      } else {
        analysis += `<div class="success">✓ TURN服务器工作正常 (${relayCandidates.length}个)</div>`;
      }
      
      analysis += '</div>';
      
      document.getElementById('iceResults').innerHTML += analysis;
    }

    async function testP2PConnection() {
      log('开始P2P连接测试...');
      
      try {
        // 创建两个PeerConnection模拟P2P连接
        pc1 = new RTCPeerConnection({
          iceServers: [
            { urls: 'stun:stun.l.google.com:19302' },
            { urls: 'turn:*************:3478', username: 'hhzt', credential: 'hhzt20130403' }
          ]
        });
        
        pc2 = new RTCPeerConnection({
          iceServers: [
            { urls: 'stun:stun.l.google.com:19302' },
            { urls: 'turn:*************:3478', username: 'hhzt', credential: 'hhzt20130403' }
          ]
        });

        // 设置事件监听
        pc1.onicecandidate = (event) => {
          if (event.candidate) {
            pc2.addIceCandidate(event.candidate);
          }
        };

        pc2.onicecandidate = (event) => {
          if (event.candidate) {
            pc1.addIceCandidate(event.candidate);
          }
        };

        pc1.oniceconnectionstatechange = () => {
          log(`PC1 ICE状态: ${pc1.iceConnectionState}`);
          updateP2PStatus();
        };

        pc2.oniceconnectionstatechange = () => {
          log(`PC2 ICE状态: ${pc2.iceConnectionState}`);
          updateP2PStatus();
        };

        // 创建数据通道
        const dataChannel = pc1.createDataChannel('test');
        dataChannel.onopen = () => {
          log('✓ 数据通道已打开', 'success');
          dataChannel.send('Hello from PC1');
        };

        pc2.ondatachannel = (event) => {
          const channel = event.channel;
          channel.onmessage = (event) => {
            log(`✓ 收到消息: ${event.data}`, 'success');
          };
        };

        // 开始连接过程
        const offer = await pc1.createOffer();
        await pc1.setLocalDescription(offer);
        await pc2.setRemoteDescription(offer);

        const answer = await pc2.createAnswer();
        await pc2.setLocalDescription(answer);
        await pc1.setRemoteDescription(answer);

        log('P2P连接协商完成，等待ICE连接...');

      } catch (error) {
        log(`P2P连接测试失败: ${error.message}`, 'error');
      }
    }

    function updateP2PStatus() {
      const resultsDiv = document.getElementById('p2pResults');
      if (pc1 && pc2) {
        const status1 = pc1.iceConnectionState;
        const status2 = pc2.iceConnectionState;
        
        let statusClass = 'info';
        if (status1 === 'connected' && status2 === 'connected') {
          statusClass = 'success';
        } else if (status1 === 'failed' || status2 === 'failed') {
          statusClass = 'error';
        }
        
        resultsDiv.innerHTML = `
          <div class="result ${statusClass}">
            <strong>P2P连接状态:</strong><br>
            PC1: ${status1}<br>
            PC2: ${status2}
          </div>
        `;
      }
    }

    // 页面加载时获取网络信息
    window.onload = () => {
      getNetworkInfo();
    };
  </script>
</body>
</html>
