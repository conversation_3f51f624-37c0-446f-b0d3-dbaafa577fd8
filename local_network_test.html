<!DOCTYPE html>
<html>
<head>
  <meta charset="UTF-8">
  <title>本地网络诊断工具</title>
  <style>
    body { font-family: Arial, sans-serif; margin: 20px; }
    .test-section { margin: 20px 0; padding: 15px; border: 1px solid #ccc; border-radius: 5px; }
    .result { margin: 10px 0; padding: 10px; border-radius: 3px; }
    .success { background-color: #d4edda; color: #155724; }
    .error { background-color: #f8d7da; color: #721c24; }
    .warning { background-color: #fff3cd; color: #856404; }
    .info { background-color: #d1ecf1; color: #0c5460; }
    button { padding: 10px 20px; margin: 5px; }
    #log { height: 300px; overflow-y: auto; border: 1px solid #ccc; padding: 10px; background: #f9f9f9; font-family: monospace; font-size: 12px; }
    .urgent { border: 2px solid #dc3545; background: #f8d7da; }
  </style>
</head>
<body>
  <h1>本地网络诊断工具</h1>
  
  <div class="test-section urgent">
    <h3>⚠️ 网络连接严重受限</h3>
    <p>检测到您的网络环境无法访问外部服务，这可能是由于：</p>
    <ul>
      <li>企业防火墙阻塞</li>
      <li>代理服务器限制</li>
      <li>网络管理策略</li>
      <li>VPN或安全软件干扰</li>
    </ul>
  </div>

  <div class="test-section">
    <h3>本地网络信息检测</h3>
    <button onclick="detectLocalNetwork()">检测本地网络</button>
    <button onclick="testDirectConnection()">测试直连服务器</button>
    <div id="localResults"></div>
  </div>

  <div class="test-section">
    <h3>代理和防火墙检测</h3>
    <button onclick="detectProxy()">检测代理设置</button>
    <button onclick="testAlternativePorts()">测试替代端口</button>
    <div id="proxyResults"></div>
  </div>

  <div class="test-section">
    <h3>WebRTC本地测试</h3>
    <button onclick="testLocalWebRTC()">测试本地WebRTC</button>
    <div id="webrtcResults"></div>
  </div>

  <div class="test-section">
    <h3>解决方案建议</h3>
    <div id="solutions">
      <div class="result warning">
        <h4>立即尝试的解决方案：</h4>
        <ol>
          <li><strong>更换网络环境</strong>：使用手机热点或家庭网络</li>
          <li><strong>检查代理设置</strong>：浏览器设置 → 高级 → 代理</li>
          <li><strong>临时关闭安全软件</strong>：防火墙、VPN、杀毒软件</li>
          <li><strong>联系网络管理员</strong>：如果是企业网络</li>
        </ol>
      </div>
    </div>
  </div>

  <div class="test-section">
    <h3>详细日志</h3>
    <div id="log"></div>
  </div>

  <script>
    function log(message, type = 'info') {
      const logDiv = document.getElementById('log');
      const timestamp = new Date().toLocaleTimeString();
      const className = type === 'error' ? 'error' : type === 'warning' ? 'warning' : type === 'success' ? 'success' : 'info';
      logDiv.innerHTML += `<div class="${className}">[${timestamp}] ${message}</div>`;
      logDiv.scrollTop = logDiv.scrollHeight;
      console.log(message);
    }

    async function detectLocalNetwork() {
      log('开始检测本地网络信息...');
      const resultsDiv = document.getElementById('localResults');
      let results = '';

      try {
        // 检测本地IP地址
        const pc = new RTCPeerConnection();
        pc.createDataChannel('test');
        
        const localIPs = [];
        
        pc.onicecandidate = (event) => {
          if (event.candidate) {
            const candidate = event.candidate.candidate;
            const ipMatch = candidate.match(/(\d+\.\d+\.\d+\.\d+)/);
            if (ipMatch && !localIPs.includes(ipMatch[1])) {
              localIPs.push(ipMatch[1]);
              log(`检测到本地IP: ${ipMatch[1]}`);
            }
          }
        };

        await pc.createOffer();
        
        setTimeout(() => {
          pc.close();
          
          if (localIPs.length > 0) {
            results += `<div class="result success">✓ 检测到本地IP地址: ${localIPs.join(', ')}</div>`;
          } else {
            results += `<div class="result error">✗ 无法检测本地IP地址（网络严重受限）</div>`;
          }
          
          // 检测网络类型
          const connection = navigator.connection || navigator.mozConnection || navigator.webkitConnection;
          if (connection) {
            results += `<div class="result info">网络类型: ${connection.effectiveType || 'unknown'}</div>`;
            results += `<div class="result info">连接类型: ${connection.type || 'unknown'}</div>`;
          }
          
          // 检测时区和语言（判断地理位置）
          results += `<div class="result info">时区: ${Intl.DateTimeFormat().resolvedOptions().timeZone}</div>`;
          results += `<div class="result info">语言: ${navigator.language}</div>`;
          
          resultsDiv.innerHTML = results;
        }, 3000);
        
      } catch (error) {
        results += `<div class="result error">本地网络检测失败: ${error.message}</div>`;
        resultsDiv.innerHTML = results;
        log(`本地网络检测失败: ${error.message}`, 'error');
      }
    }

    async function testDirectConnection() {
      log('测试直连服务器...');
      const resultsDiv = document.getElementById('localResults');
      
      // 尝试不同的连接方式
      const tests = [
        { url: 'http://60.255.197.32:8188/', name: 'HTTP直连8188' },
        { url: 'http://60.255.197.32:8088/', name: 'HTTP直连8088' },
        { url: 'http://60.255.197.32/', name: 'HTTP直连80' }
      ];

      let results = resultsDiv.innerHTML;

      for (const test of tests) {
        try {
          const controller = new AbortController();
          const timeoutId = setTimeout(() => controller.abort(), 5000);
          
          const response = await fetch(test.url, {
            method: 'HEAD',
            mode: 'no-cors',
            signal: controller.signal
          });
          
          clearTimeout(timeoutId);
          results += `<div class="result success">✓ ${test.name} 连接成功</div>`;
          log(`${test.name} 连接成功`);
          
        } catch (error) {
          if (error.name === 'AbortError') {
            results += `<div class="result error">✗ ${test.name} 连接超时</div>`;
            log(`${test.name} 连接超时`, 'error');
          } else {
            results += `<div class="result error">✗ ${test.name} 连接失败: ${error.message}</div>`;
            log(`${test.name} 连接失败: ${error.message}`, 'error');
          }
        }
      }

      resultsDiv.innerHTML = results;
    }

    function detectProxy() {
      log('检测代理设置...');
      const resultsDiv = document.getElementById('proxyResults');
      let results = '';

      // 检测常见的代理指示器
      const proxyHeaders = [
        'HTTP_X_FORWARDED_FOR',
        'HTTP_X_REAL_IP',
        'HTTP_CLIENT_IP',
        'HTTP_X_FORWARDED',
        'HTTP_FORWARDED_FOR',
        'HTTP_FORWARDED'
      ];

      // 检测代理端口
      const commonProxyPorts = [8080, 3128, 8888, 8118, 9050];
      
      results += `<div class="result info">常见代理端口检测:</div>`;
      
      // 检测浏览器代理设置（间接方法）
      const startTime = Date.now();
      
      // 尝试访问一个应该很快响应的本地资源
      fetch('data:text/plain,test')
        .then(() => {
          const responseTime = Date.now() - startTime;
          if (responseTime > 1000) {
            results += `<div class="result warning">⚠️ 响应时间异常 (${responseTime}ms)，可能存在代理</div>`;
          } else {
            results += `<div class="result success">✓ 本地响应正常 (${responseTime}ms)</div>`;
          }
        })
        .catch(error => {
          results += `<div class="result error">✗ 本地访问异常: ${error.message}</div>`;
        });

      // 检测WebSocket支持
      try {
        const ws = new WebSocket('ws://echo.websocket.org/');
        ws.onopen = () => {
          results += `<div class="result success">✓ WebSocket外部连接正常</div>`;
          ws.close();
        };
        ws.onerror = () => {
          results += `<div class="result error">✗ WebSocket外部连接失败（可能被代理阻塞）</div>`;
        };
      } catch (error) {
        results += `<div class="result error">✗ WebSocket不可用: ${error.message}</div>`;
      }

      setTimeout(() => {
        resultsDiv.innerHTML = results;
      }, 2000);
    }

    async function testAlternativePorts() {
      log('测试替代端口...');
      const resultsDiv = document.getElementById('proxyResults');
      
      // 测试常见的开放端口
      const ports = [80, 443, 8080, 8443, 9000];
      let results = resultsDiv.innerHTML;

      results += `<div class="result info">测试服务器替代端口:</div>`;

      for (const port of ports) {
        try {
          const controller = new AbortController();
          const timeoutId = setTimeout(() => controller.abort(), 3000);
          
          await fetch(`http://60.255.197.32:${port}/`, {
            method: 'HEAD',
            mode: 'no-cors',
            signal: controller.signal
          });
          
          clearTimeout(timeoutId);
          results += `<div class="result success">✓ 端口 ${port} 可访问</div>`;
          log(`端口 ${port} 可访问`);
          
        } catch (error) {
          results += `<div class="result error">✗ 端口 ${port} 不可访问</div>`;
        }
      }

      resultsDiv.innerHTML = results;
    }

    async function testLocalWebRTC() {
      log('测试本地WebRTC功能...');
      const resultsDiv = document.getElementById('webrtcResults');
      let results = '';

      try {
        // 测试本地媒体访问
        const stream = await navigator.mediaDevices.getUserMedia({ video: true, audio: true });
        results += `<div class="result success">✓ 摄像头和麦克风访问正常</div>`;
        stream.getTracks().forEach(track => track.stop());
        
        // 测试本地P2P连接
        const pc1 = new RTCPeerConnection();
        const pc2 = new RTCPeerConnection();
        
        const dataChannel = pc1.createDataChannel('test');
        
        pc1.onicecandidate = (event) => {
          if (event.candidate) {
            pc2.addIceCandidate(event.candidate);
          }
        };
        
        pc2.onicecandidate = (event) => {
          if (event.candidate) {
            pc1.addIceCandidate(event.candidate);
          }
        };
        
        dataChannel.onopen = () => {
          results += `<div class="result success">✓ 本地P2P连接正常</div>`;
          dataChannel.send('test message');
        };
        
        pc2.ondatachannel = (event) => {
          event.channel.onmessage = () => {
            results += `<div class="result success">✓ 本地数据传输正常</div>`;
            pc1.close();
            pc2.close();
          };
        };
        
        const offer = await pc1.createOffer();
        await pc1.setLocalDescription(offer);
        await pc2.setRemoteDescription(offer);
        
        const answer = await pc2.createAnswer();
        await pc2.setLocalDescription(answer);
        await pc1.setRemoteDescription(answer);
        
        setTimeout(() => {
          resultsDiv.innerHTML = results;
        }, 3000);
        
      } catch (error) {
        results += `<div class="result error">✗ WebRTC测试失败: ${error.message}</div>`;
        resultsDiv.innerHTML = results;
        log(`WebRTC测试失败: ${error.message}`, 'error');
      }
    }

    // 页面加载时显示紧急提示
    window.onload = function() {
      log('检测到严重的网络连接问题');
      log('建议立即尝试更换网络环境（如手机热点）');
      
      // 自动检测基本信息
      setTimeout(detectLocalNetwork, 1000);
    };
  </script>
</body>
</html>
