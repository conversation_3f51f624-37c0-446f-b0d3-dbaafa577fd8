# 监听所有接口
listening-port=3478
# 同时监听TCP端口
alt-listening-port=3479
# 指定公网IP
relay-ip=*************

# 推荐同时监听TCP
# tls-listening-port=5349
# 证书配置（如不需要TLS可不配）
# cert=/etc/turn_server_cert.pem
# pkey=/etc/turn_server_pkey.pem

# 用户名密码认证
user=hhzt:hhzt20130403
# 或用长期凭证机制
lt-cred-mech

realm=hhztrealm

# relay端口区间（必须云服务器安全组和本机防火墙全部放行！）
min-port=49152
max-port=65535

# 外部IP地址（公网IP）
external-ip=*************

# 日志级别
verbose

# 禁用IPv6支持
no-ipv6-relay
fingerprint

# 允许UDP和TCP中继（适配各种环境）
# 通常默认已开，无需专门配置

# 允许所有来源IP
no-auth-pings
no-software-attribute

# 启用更详细的日志
log-file=/var/log/turnserver.log

# 解决ICE连接问题的额外配置
mobility
no-cli
no-tls
no-dtls

# 允许所有源地址
no-multicast-peers

# 启用详细统计
proc-user=root
proc-group=root
