<!DOCTYPE html>
<html>
<head>
    <title>修复静音问题测试</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .container { max-width: 1200px; margin: 0 auto; }
        .video-container { display: flex; gap: 20px; margin: 20px 0; }
        video { width: 300px; height: 200px; border: 1px solid #ccc; }
        .log { background: #f5f5f5; padding: 10px; height: 400px; overflow-y: scroll; font-family: monospace; font-size: 12px; }
        button { padding: 10px 20px; margin: 5px; font-size: 16px; }
        .status { padding: 10px; margin: 10px 0; border-radius: 5px; }
        .success { background: #d4edda; color: #155724; }
        .error { background: #f8d7da; color: #721c24; }
        .info { background: #d1ecf1; color: #0c5460; }
    </style>
</head>
<body>
    <div class="container">
        <h1>修复静音问题测试 - 192.168.1.2 专用</h1>
        
        <div class="video-container">
            <div>
                <h3>本地视频</h3>
                <video id="localVideo" autoplay muted></video>
            </div>
            <div>
                <h3>远程视频</h3>
                <video id="remoteVideo" autoplay></video>
            </div>
        </div>
        
        <div>
            <button onclick="startTest()">开始修复测试</button>
            <button onclick="clearLog()">清空日志</button>
        </div>
        
        <div id="status" class="status info">准备开始修复测试...</div>
        
        <div class="log" id="log"></div>
    </div>

    <script src="https://webrtc.github.io/adapter/adapter-latest.js"></script>
    <script src="https://janus.conf.meetecho.com/janus.js"></script>
    
    <script>
        let janus = null;
        let videoroom = null;
        let localStream = null;
        let myId = null;
        let subscribedFeeds = new Set();
        
        function log(message) {
            const timestamp = new Date().toLocaleTimeString();
            const logDiv = document.getElementById('log');
            logDiv.innerHTML += `[${timestamp}] ${message}\n`;
            logDiv.scrollTop = logDiv.scrollHeight;
            console.log(message);
        }
        
        function updateStatus(message, type = 'info') {
            const statusDiv = document.getElementById('status');
            statusDiv.textContent = message;
            statusDiv.className = `status ${type}`;
        }
        
        function clearLog() {
            document.getElementById('log').innerHTML = '';
        }
        
        async function startTest() {
            try {
                log('🔧 开始修复静音问题测试...');

                // 检查库是否加载
                if (typeof adapter === 'undefined') {
                    log('❌ adapter.js 未加载');
                    updateStatus('adapter.js 未加载', 'error');
                    return;
                }
                log('✓ adapter.js已加载');

                if (typeof Janus === 'undefined') {
                    log('❌ janus.js 未加载');
                    updateStatus('janus.js 未加载', 'error');
                    return;
                }
                log('✓ janus.js已加载');

                updateStatus('正在获取摄像头...', 'info');

                localStream = await navigator.mediaDevices.getUserMedia({
                    video: { width: 640, height: 480 },
                    audio: true
                });

                document.getElementById('localVideo').srcObject = localStream;
                log('✅ 本地媒体流获取成功');

                updateStatus('初始化 Janus...', 'info');
                initJanus();

            } catch (error) {
                log(`❌ 错误: ${error.message}`);
                updateStatus(`错误: ${error.message}`, 'error');
            }
        }
        
        function initJanus() {
            Janus.init({
                debug: "all",
                callback: function() {
                    log('✅ Janus 初始化成功');
                    
                    janus = new Janus({
                        server: "ws://60.255.197.32:8188/",
                        success: function() {
                            log('✅ Janus 连接成功');
                            updateStatus('连接 VideoRoom...', 'info');
                            attachVideoRoom();
                        },
                        error: function(error) {
                            log(`❌ Janus 连接失败: ${error}`);
                            updateStatus(`Janus 连接失败: ${error}`, 'error');
                        }
                    });
                }
            });
        }
        
        function attachVideoRoom() {
            janus.attach({
                plugin: "janus.plugin.videoroom",
                success: function(pluginHandle) {
                    videoroom = pluginHandle;
                    log('✅ VideoRoom 插件连接成功');
                    
                    const register = {
                        request: "join",
                        room: 1234,
                        ptype: "publisher",
                        display: "修复测试用户"
                    };
                    
                    videoroom.send({ message: register });
                    log('📤 发送加入房间请求');
                },
                error: function(error) {
                    log(`❌ VideoRoom 连接失败: ${error}`);
                },
                onmessage: function(msg, jsep) {
                    log(`📨 收到消息: ${JSON.stringify(msg)}`);
                    
                    if (msg["videoroom"] === "joined") {
                        myId = msg["id"];
                        log(`✅ 已加入房间，ID: ${myId}`);
                        
                        // 检查是否有其他发布者
                        if (msg["publishers"] && msg["publishers"].length > 0) {
                            msg["publishers"].forEach(publisher => {
                                log(`发现发布者: ${publisher.display} (${publisher.id})`);
                                subscribeToFeed(publisher.id, publisher.display);
                            });
                        }
                        
                        // 开始发布
                        publishOwnFeed();
                    }
                    
                    if (msg["videoroom"] === "event") {
                        if (msg["publishers"]) {
                            msg["publishers"].forEach(publisher => {
                                log(`新发布者: ${publisher.display} (${publisher.id})`);
                                subscribeToFeed(publisher.id, publisher.display);
                            });
                        }
                    }
                    
                    if (jsep) {
                        log(`📥 处理 SDP: ${jsep.type}`);
                        videoroom.handleRemoteJsep({ jsep: jsep });
                    }
                },
                webrtcState: function(on) {
                    log(`🔗 发布者 WebRTC 状态: ${on ? '已连接' : '已断开'}`);
                    if (on) {
                        updateStatus('发布成功！', 'success');
                    }
                }
            });
        }
        
        function publishOwnFeed() {
            videoroom.createOffer({
                media: {
                    audioRecv: false,
                    videoRecv: false,
                    audioSend: true,
                    videoSend: true
                },
                stream: localStream,
                success: function(jsep) {
                    log('✅ Offer 创建成功');
                    const publish = { request: "configure", audio: true, video: true };
                    videoroom.send({ message: publish, jsep: jsep });
                },
                error: function(error) {
                    log(`❌ Offer 创建失败: ${error}`);
                }
            });
        }
        
        function subscribeToFeed(id, display) {
            if (subscribedFeeds.has(id)) {
                log(`⚠️ 已订阅 ${display}，跳过`);
                return;
            }
            
            subscribedFeeds.add(id);
            log(`🔄 开始订阅 ${display} (ID: ${id}) 的流...`);
            
            let remoteFeed = null;
            
            janus.attach({
                plugin: "janus.plugin.videoroom",
                success: function(pluginHandle) {
                    remoteFeed = pluginHandle;
                    log(`✅ 订阅插件连接成功`);
                    
                    const subscribe = {
                        request: "join",
                        room: 1234,
                        ptype: "subscriber",
                        feed: id,
                        private_id: Date.now() + Math.floor(Math.random() * 1000000)
                    };
                    
                    remoteFeed.send({ message: subscribe });
                },
                error: function(error) {
                    log(`❌ 订阅插件连接失败: ${error}`);
                },
                // 使用新的 onremotetrack API 而不是 onremotestream
                onremotetrack: function(event) {
                    const track = event.track;
                    const mid = event.transceiver ? event.transceiver.mid : null;
                    
                    log(`🎬 收到远程轨道: ${track.kind}, mid: ${mid}`);
                    log(`🔍 轨道状态: enabled=${track.enabled}, muted=${track.muted}, readyState=${track.readyState}`);
                    
                    // 关键修复：立即处理轨道，不等待状态变化
                    const remoteVideo = document.getElementById('remoteVideo');
                    let remoteStream = remoteVideo.srcObject;
                    
                    if (!remoteStream) {
                        remoteStream = new MediaStream();
                        remoteVideo.srcObject = remoteStream;
                        log(`📺 创建新的远程流`);
                    }
                    
                    // 直接添加轨道，不检查 muted 状态
                    const existingTracks = remoteStream.getTracks().filter(t => t.kind === track.kind);
                    if (existingTracks.length === 0) {
                        remoteStream.addTrack(track);
                        log(`📹 强制添加${track.kind}轨道到远程流`);
                        
                        if (track.kind === 'video') {
                            // 强制播放
                            setTimeout(() => {
                                remoteVideo.play().then(() => {
                                    log(`🎉 修复成功！远程视频开始播放`);
                                    updateStatus(`✅ 修复成功！正在显示 ${display} 的视频`, 'success');
                                }).catch(e => {
                                    log(`❌ 播放失败: ${e.message}`);
                                });
                            }, 100);
                        }
                    }
                    
                    // 监听但不阻止静音
                    track.onmute = () => {
                        log(`🔇 轨道 ${track.kind} 被静音 - 但我们已经添加到流中了`);
                    };
                    track.onunmute = () => {
                        log(`🔊 轨道 ${track.kind} 取消静音`);
                    };
                },
                onmessage: function(msg, jsep) {
                    if (jsep) {
                        remoteFeed.createAnswer({
                            jsep: jsep,
                            // 关键修复：强制接收所有媒体
                            media: { 
                                audioSend: false, 
                                videoSend: false,
                                audioRecv: true,
                                videoRecv: true,
                                // 强制不移除媒体
                                removeVideo: false,
                                removeAudio: false
                            },
                            success: function(answerJsep) {
                                const body = { request: "start", room: 1234 };
                                remoteFeed.send({ message: body, jsep: answerJsep });
                                log(`✅ 修复版订阅应答发送成功`);
                            },
                            error: function(error) {
                                log(`❌ 创建应答失败: ${error}`);
                            }
                        });
                    }
                },
                webrtcState: function(on) {
                    log(`📺 订阅 ${display} WebRTC: ${on ? '已连接' : '已断开'}`);
                    // 关键修复：即使断开也不清理，让轨道保持
                    return false;
                }
            });
        }
    </script>
</body>
</html>
