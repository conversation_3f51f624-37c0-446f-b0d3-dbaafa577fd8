#!/bin/bash

echo "=== Janus启动测试脚本 ==="
echo "当前时间: $(date)"

# 检查Janus可执行文件
JANUS_BIN="/opt/janus/bin/janus"
if [ ! -f "$JANUS_BIN" ]; then
    echo "错误: 找不到Janus可执行文件: $JANUS_BIN"
    exit 1
fi

echo "找到Janus可执行文件: $JANUS_BIN"

# 检查配置文件
CONFIG_FILE="./janus.jcfg"
if [ ! -f "$CONFIG_FILE" ]; then
    echo "错误: 找不到配置文件: $CONFIG_FILE"
    exit 1
fi

echo "找到配置文件: $CONFIG_FILE"

# 检查网络连通性
echo ""
echo "=== 网络连通性测试 ==="

# 测试Google DNS
echo "测试Google DNS连通性..."
if ping -c 1 8.8.8.8 >/dev/null 2>&1; then
    echo "✓ Google DNS (8.8.8.8) 连通"
else
    echo "✗ Google DNS (8.8.8.8) 不通"
fi

# 测试STUN服务器
echo "测试STUN服务器连通性..."
if ping -c 1 stun.l.google.com >/dev/null 2>&1; then
    echo "✓ STUN服务器 (stun.l.google.com) 连通"
else
    echo "✗ STUN服务器 (stun.l.google.com) 不通"
fi

# 检查端口占用
echo ""
echo "=== 端口占用检查 ==="
echo "检查8188端口:"
if netstat -tlnp | grep :8188; then
    echo "警告: 端口8188已被占用"
else
    echo "✓ 端口8188可用"
fi

echo "检查RTP端口范围 (20000-20010):"
PORTS_IN_USE=$(netstat -tlnp | grep -E ":200[0-9][0-9]" | wc -l)
echo "RTP端口范围内已使用端口数: $PORTS_IN_USE"

# 尝试启动Janus (测试模式)
echo ""
echo "=== Janus配置测试 ==="
echo "测试Janus配置文件语法..."

# 创建临时最小配置
cat > /tmp/janus_test.jcfg << 'EOF'
general: {
    configs_folder = "/opt/janus/etc/janus"
    plugins_folder = "/opt/janus/lib/janus/plugins"
    transports_folder = "/opt/janus/lib/janus/transports"
    events_folder = "/opt/janus/lib/janus/events"
    loggers_folder = "/opt/janus/lib/janus/loggers"
    debug_level = 4
    admin_secret = "janusoverlord"
}

certificates: {
}

media: {
    rtp_port_range = "20000-40000"
}

nat: {
    nice_debug = false
    nat_1_1_mapping = "60.255.197.32"
    ice_enforce_list = "60.255.197.32"
}

plugins: {
}

transports: {
}

loggers: {
}

events: {
}
EOF

echo "使用最小配置测试Janus启动..."
timeout 10s $JANUS_BIN -C /tmp/janus_test.jcfg &
JANUS_PID=$!
sleep 3

if ps -p $JANUS_PID > /dev/null 2>&1; then
    echo "✓ Janus最小配置启动成功"
    kill $JANUS_PID
    wait $JANUS_PID 2>/dev/null
else
    echo "✗ Janus最小配置启动失败"
fi

# 清理
rm -f /tmp/janus_test.jcfg

echo ""
echo "=== 建议的启动命令 ==="
echo "如果上述测试通过，请使用以下命令启动Janus:"
echo ""
echo "cd /opt/janus"
echo "bin/janus -C $(pwd)/janus.jcfg"
echo ""
echo "或者使用简化配置:"
echo "bin/janus -C $(pwd)/janus_simple.jcfg"
echo ""
echo "=== 故障排除提示 ==="
echo "1. 如果STUN服务器不通，这是正常的，Janus可以在没有STUN的情况下工作"
echo "2. 确保防火墙允许端口8188和20000-40000"
echo "3. 如果仍有问题，请检查/opt/janus/etc/janus/目录下的插件配置文件"
echo "4. 可以尝试降低debug_level到2或3"
