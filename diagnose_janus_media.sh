#!/bin/bash

echo "=== Janus 媒体流诊断脚本 ==="
echo "时间: $(date)"
echo ""

# 检查 Janus 进程
echo "1. 检查 Janus 进程状态"
echo "========================="
if pgrep -f janus > /dev/null; then
    echo "✅ Janus 进程正在运行"
    ps aux | grep janus | grep -v grep
else
    echo "❌ Janus 进程未运行"
    exit 1
fi
echo ""

# 检查端口监听
echo "2. 检查端口监听状态"
echo "==================="
echo "WebSocket 端口 (8188):"
if netstat -tlnp | grep :8188; then
    echo "✅ WebSocket 端口正常监听"
else
    echo "❌ WebSocket 端口未监听"
fi

echo ""
echo "RTP 端口范围 (20000-40000):"
RTP_PORTS=$(netstat -unlp | grep -E ":2[0-9][0-9][0-9][0-9]|:3[0-9][0-9][0-9][0-9]" | wc -l)
echo "RTP 端口使用数量: $RTP_PORTS"
if [ $RTP_PORTS -gt 0 ]; then
    echo "✅ 有 RTP 端口在使用"
    netstat -unlp | grep -E ":2[0-9][0-9][0-9][0-9]|:3[0-9][0-9][0-9][0-9]" | head -5
else
    echo "⚠️  没有 RTP 端口在使用"
fi
echo ""

# 检查 VideoRoom 插件
echo "3. 检查 VideoRoom 插件"
echo "======================"
VIDEOROOM_SO="/opt/janus/lib/janus/plugins/libjanus_videoroom.so"
if [ -f "$VIDEOROOM_SO" ]; then
    echo "✅ VideoRoom 插件文件存在"
    ls -la "$VIDEOROOM_SO"
else
    echo "❌ VideoRoom 插件文件不存在"
fi

# 检查 VideoRoom 配置
VIDEOROOM_CFG="/opt/janus/etc/janus/janus.plugin.videoroom.jcfg"
if [ -f "$VIDEOROOM_CFG" ]; then
    echo "✅ VideoRoom 配置文件存在"
    echo "配置内容:"
    cat "$VIDEOROOM_CFG"
else
    echo "⚠️  VideoRoom 配置文件不存在，使用默认配置"
fi
echo ""

# 检查日志中的错误
echo "4. 检查 Janus 日志"
echo "=================="
if [ -f "/var/log/janus.log" ]; then
    echo "最近的错误日志:"
    tail -20 /var/log/janus.log | grep -i "error\|fail\|warn"
elif [ -f "/opt/janus/janus.log" ]; then
    echo "最近的错误日志:"
    tail -20 /opt/janus/janus.log | grep -i "error\|fail\|warn"
else
    echo "⚠️  未找到 Janus 日志文件"
fi
echo ""

# 检查媒体编解码器支持
echo "5. 检查媒体编解码器支持"
echo "======================"
echo "检查 VP8 编解码器支持:"
if ldd /opt/janus/lib/janus/plugins/libjanus_videoroom.so | grep -i vpx; then
    echo "✅ VP8 编解码器支持"
else
    echo "⚠️  VP8 编解码器支持未确认"
fi

echo ""
echo "检查 H.264 编解码器支持:"
if ldd /opt/janus/lib/janus/plugins/libjanus_videoroom.so | grep -i x264; then
    echo "✅ H.264 编解码器支持"
else
    echo "⚠️  H.264 编解码器支持未确认"
fi
echo ""

# 检查网络连接
echo "6. 检查网络连接"
echo "==============="
echo "当前活跃的 WebRTC 连接:"
netstat -an | grep -E ":2[0-9][0-9][0-9][0-9]|:3[0-9][0-9][0-9][0-9]" | grep ESTABLISHED | wc -l
echo ""

# 实时监控 RTP 流量
echo "7. 实时 RTP 流量监控 (5秒)"
echo "=========================="
echo "监控 RTP 端口范围的流量..."
timeout 5 tcpdump -i any -c 20 udp portrange 20000-40000 2>/dev/null | head -10
echo ""

echo "=== 诊断完成 ==="
echo ""
echo "建议检查项目:"
echo "1. 如果没有 RTP 流量，检查客户端是否正确发送媒体"
echo "2. 如果有连接但没有媒体，检查编解码器配置"
echo "3. 检查防火墙是否阻止了 RTP 端口"
echo "4. 检查 Janus 日志中的详细错误信息"
