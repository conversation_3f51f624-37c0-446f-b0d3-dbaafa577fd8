#!/bin/bash

echo "=== WebRTC问题修复脚本 ==="
echo "当前时间: $(date)"

# 检查是否以root权限运行
if [ "$EUID" -ne 0 ]; then
    echo "请以root权限运行此脚本"
    echo "使用: sudo $0"
    exit 1
fi

# 1. 停止现有服务
echo ""
echo "1. 停止现有服务..."
pkill -f janus
pkill -f turnserver
sleep 3

# 2. 清理旧的日志文件
echo ""
echo "2. 清理旧日志..."
rm -f /var/log/turn_*.log
rm -f /var/log/turnserver.log

# 3. 检查防火墙设置
echo ""
echo "3. 检查防火墙设置..."
if command -v firewall-cmd &> /dev/null; then
    echo "配置firewalld..."
    firewall-cmd --permanent --add-port=8188/tcp
    firewall-cmd --permanent --add-port=3478/tcp
    firewall-cmd --permanent --add-port=3478/udp
    firewall-cmd --permanent --add-port=3479/tcp
    firewall-cmd --permanent --add-port=20000-40000/udp
    firewall-cmd --permanent --add-port=49152-65535/udp
    firewall-cmd --reload
    echo "防火墙规则已更新"
elif command -v ufw &> /dev/null; then
    echo "配置ufw..."
    ufw allow 8188/tcp
    ufw allow 3478
    ufw allow 3479/tcp
    ufw allow 20000:40000/udp
    ufw allow 49152:65535/udp
    echo "防火墙规则已更新"
else
    echo "未检测到防火墙管理工具，请手动配置防火墙"
fi

# 4. 启动TURN服务器
echo ""
echo "4. 启动TURN服务器..."
cd /root/janus
turnserver -c turnserver.conf -v &
TURN_PID=$!
echo "TURN服务器已启动，PID: $TURN_PID"
sleep 3

# 验证TURN服务器
if ps -p $TURN_PID > /dev/null; then
    echo "✓ TURN服务器启动成功"
    echo "检查端口监听:"
    netstat -tlnp | grep :3478
    netstat -tlnp | grep :3479
else
    echo "✗ TURN服务器启动失败"
    exit 1
fi

# 5. 启动Janus
echo ""
echo "5. 启动Janus服务器..."
cd /opt/janus
bin/janus -C /root/janus/janus.jcfg &
JANUS_PID=$!
echo "Janus服务器已启动，PID: $JANUS_PID"
sleep 5

# 验证Janus
if ps -p $JANUS_PID > /dev/null; then
    echo "✓ Janus服务器启动成功"
    echo "检查端口监听:"
    netstat -tlnp | grep :8188
else
    echo "✗ Janus服务器启动失败"
    exit 1
fi

# 6. 测试TURN服务器连通性
echo ""
echo "6. 测试TURN服务器连通性..."

# 创建简单的TURN测试
cat > /tmp/test_turn.py << 'EOF'
#!/usr/bin/env python3
import socket
import struct
import hashlib
import hmac

def test_turn_server():
    try:
        # 创建UDP socket
        sock = socket.socket(socket.AF_INET, socket.SOCK_DGRAM)
        sock.settimeout(5)
        
        # STUN Binding Request
        msg_type = 0x0001  # Binding Request
        msg_length = 0x0000
        magic_cookie = 0x2112A442
        transaction_id = b'\x00\x01\x02\x03\x04\x05\x06\x07\x08\x09\x0a\x0b'
        
        # 构建STUN消息
        stun_msg = struct.pack('!HHI', msg_type, msg_length, magic_cookie) + transaction_id
        
        # 发送到TURN服务器
        sock.sendto(stun_msg, ('60.255.197.32', 3478))
        
        # 接收响应
        response, addr = sock.recvfrom(1024)
        
        if len(response) >= 20:
            print("✓ TURN服务器响应正常")
            return True
        else:
            print("✗ TURN服务器响应异常")
            return False
            
    except Exception as e:
        print(f"✗ TURN服务器测试失败: {e}")
        return False
    finally:
        sock.close()

if __name__ == "__main__":
    test_turn_server()
EOF

python3 /tmp/test_turn.py
rm -f /tmp/test_turn.py

# 7. 显示服务状态
echo ""
echo "7. 服务状态总结..."
echo "TURN服务器进程:"
ps aux | grep turnserver | grep -v grep

echo ""
echo "Janus服务器进程:"
ps aux | grep janus | grep -v grep

echo ""
echo "端口监听状态:"
echo "8188 (Janus WebSocket):"
netstat -tlnp | grep :8188

echo "3478 (TURN UDP):"
netstat -tlnp | grep :3478

echo "3479 (TURN TCP):"
netstat -tlnp | grep :3479

# 8. 显示日志位置
echo ""
echo "8. 日志文件位置..."
echo "TURN日志: /var/log/turnserver.log"
echo "Janus日志: 控制台输出"

echo ""
echo "=== 修复完成 ==="
echo ""
echo "下一步测试:"
echo "1. 打开浏览器访问 test_webrtc_connectivity.html"
echo "2. 在两台客户端机器上测试视频通话"
echo "3. 如果问题仍存在，请查看日志:"
echo "   tail -f /var/log/turnserver.log"
echo ""
echo "如果需要查看实时日志:"
echo "   tail -f /var/log/turnserver.log &"
echo "   tail -f /opt/janus/janus.log &"
