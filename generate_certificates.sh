#!/bin/bash

echo "=== 生成 Janus DTLS 证书 ==="
echo "时间: $(date)"
echo ""

CERT_DIR="/opt/janus/etc/janus"
CERT_FILE="$CERT_DIR/dtls-cert.pem"
KEY_FILE="$CERT_DIR/dtls-key.pem"

# 创建证书目录
mkdir -p "$CERT_DIR"

# 生成私钥和自签名证书
echo "1. 生成 DTLS 私钥和证书..."
openssl req -new -x509 -days 365 -nodes \
    -out "$CERT_FILE" \
    -keyout "$KEY_FILE" \
    -subj "/C=CN/ST=Beijing/L=Beijing/O=Janus/OU=WebRTC/CN=60.255.197.32" 2>/dev/null

if [ $? -eq 0 ]; then
    echo "✅ 证书生成成功"
    echo "证书文件: $CERT_FILE"
    echo "私钥文件: $KEY_FILE"
else
    echo "❌ 证书生成失败，尝试使用系统 openssl..."
    
    # 尝试使用系统的 openssl
    /usr/bin/openssl req -new -x509 -days 365 -nodes \
        -out "$CERT_FILE" \
        -keyout "$KEY_FILE" \
        -subj "/C=CN/ST=Beijing/L=Beijing/O=Janus/OU=WebRTC/CN=60.255.197.32"
    
    if [ $? -eq 0 ]; then
        echo "✅ 使用系统 openssl 生成证书成功"
    else
        echo "❌ 证书生成失败"
        exit 1
    fi
fi

# 设置权限
chmod 600 "$KEY_FILE"
chmod 644 "$CERT_FILE"

echo ""
echo "2. 验证证书..."
if [ -f "$CERT_FILE" ] && [ -f "$KEY_FILE" ]; then
    echo "✅ 证书文件存在"
    echo "证书信息:"
    /usr/bin/openssl x509 -in "$CERT_FILE" -text -noout | grep -E "Subject:|Not Before:|Not After:" 2>/dev/null || echo "无法读取证书信息"
else
    echo "❌ 证书文件不存在"
    exit 1
fi

echo ""
echo "=== 证书生成完成 ==="
