<!DOCTYPE html>
<html>
<head>
  <title>RTSP + VideoRoom 客户端</title>
  <style>
    body { font-family: Arial, sans-serif; margin: 20px; }
    .container { max-width: 1200px; margin: 0 auto; }
    .video-container { display: flex; flex-wrap: wrap; gap: 20px; margin: 20px 0; }
    .video-box { border: 2px solid #ccc; padding: 10px; border-radius: 5px; }
    .rtsp-video { border-color: #007bff; }
    .user-video { border-color: #28a745; }
    video { width: 320px; height: 240px; background: #000; }
    .controls { margin: 20px 0; }
    button { padding: 10px 20px; margin: 5px; font-size: 16px; cursor: pointer; }
    .button-primary { background: #007bff; color: white; border: none; border-radius: 4px; }
    .button-success { background: #28a745; color: white; border: none; border-radius: 4px; }
    .button-danger { background: #dc3545; color: white; border: none; border-radius: 4px; }
    #log { height: 300px; overflow-y: scroll; border: 1px solid #ccc; padding: 10px; 
           font-family: monospace; font-size: 12px; background: #f8f9fa; }
    .status { padding: 10px; margin: 10px 0; border-radius: 4px; }
    .status.success { background: #d4edda; color: #155724; border: 1px solid #c3e6cb; }
    .status.error { background: #f8d7da; color: #721c24; border: 1px solid #f5c6cb; }
    .status.info { background: #d1ecf1; color: #0c5460; border: 1px solid #bee5eb; }
  </style>
</head>
<body>
  <div class="container">
    <h1>RTSP + VideoRoom 混合客户端</h1>
    <div id="status" class="status info">准备连接...</div>
    
    <div class="controls">
      <button id="start" class="button-primary" onclick="start()">开始</button>
      <button id="stop" class="button-danger" onclick="stop()" disabled>停止</button>
      <button id="startRtsp" class="button-success" onclick="startRtspStream()" disabled>启动RTSP流</button>
      <span style="margin-left: 20px;">房间号: <strong>1000</strong> | RTSP流ID: <strong>99</strong></span>
    </div>
    
    <div class="video-container">
      <div class="video-box rtsp-video">
        <h3>RTSP 监控流</h3>
        <video id="rtspVideo" autoplay playsinline></video>
        <div>来源: ************:554</div>
      </div>
      
      <div class="video-box user-video">
        <h3>本地摄像头</h3>
        <video id="localVideo" autoplay playsinline muted="muted"></video>
      </div>
      
      <div class="video-box user-video">
        <h3>其他用户视频</h3>
        <video id="remoteVideo" autoplay playsinline></video>
      </div>
    </div>
    
    <div>
      <h3>调试日志</h3>
      <div id="log"></div>
    </div>
  </div>

  <script src="adapter-latest.js"></script>
  <script src="janus.js"></script>
  
  <script>
    // 全局变量
    var janus = null;
    var videoroom = null;
    var streaming = null;
    var opaqueId = "rtsproom-" + Janus.randomString(12);
    var mystream = null;
    var myid = null;
    var feeds = [];
    var rtspStreamId = 99;  // RTSP 流 ID

    // 日志函数
    function log(msg) {
      var time = new Date().toLocaleTimeString();
      document.getElementById('log').innerHTML += '[' + time + '] ' + msg + '<br>';
      document.getElementById('log').scrollTop = document.getElementById('log').scrollHeight;
      console.log(msg);
    }

    // 状态更新
    function updateStatus(msg, type) {
      var status = document.getElementById('status');
      status.textContent = msg;
      status.className = 'status ' + (type || 'info');
      log('状态: ' + msg);
    }

    // 开始函数
    function start() {
      document.getElementById('start').disabled = true;
      updateStatus('正在初始化 Janus...', 'info');
      
      // 初始化 Janus
      Janus.init({
        debug: "all",
        callback: function() {
          log('Janus 初始化成功');
          
          // 创建 Janus 会话
          janus = new Janus({
            server: 'ws://60.255.197.32:8188/',
            success: function() {
              log('Janus 连接成功');
              updateStatus('Janus 连接成功', 'success');
              
              // 先连接 VideoRoom
              attachVideoRoom();
            },
            error: function(error) {
              log('Janus 连接失败: ' + error);
              updateStatus('Janus 连接失败: ' + error, 'error');
            },
            destroyed: function() {
              log('Janus 连接已断开');
            }
          });
        }
      });
    }

    // 连接 VideoRoom 插件
    function attachVideoRoom() {
      janus.attach({
        plugin: "janus.plugin.videoroom",
        opaqueId: opaqueId,
        success: function(pluginHandle) {
          videoroom = pluginHandle;
          log('VideoRoom 插件连接成功');
          updateStatus('正在获取摄像头...', 'info');
          
          // 获取用户媒体
          navigator.mediaDevices.getUserMedia({
            audio: true,
            video: true
          }).then(function(stream) {
            log('摄像头获取成功');
            mystream = stream;
            document.getElementById('localVideo').srcObject = stream;
            
            // 加入房间
            joinRoom();
          }).catch(function(error) {
            log('摄像头获取失败: ' + error);
            // 即使没有摄像头也可以加入房间观看 RTSP 流
            joinRoomWithoutCamera();
          });
        },
        error: function(error) {
          log('VideoRoom 插件连接失败: ' + error);
          updateStatus('VideoRoom 插件连接失败: ' + error, 'error');
        },
        onmessage: function(msg, jsep) {
          log('收到 VideoRoom 消息: ' + JSON.stringify(msg));
          var event = msg["videoroom"];
          
          if(event != undefined && event != null) {
            if(event === "joined") {
              // 成功加入房间
              myid = msg["id"];
              log('成功加入房间，ID: ' + myid);
              updateStatus('已加入房间', 'success');
              
              // 如果有本地流，发布它
              if(mystream) {
                publishOwnFeed(true);
              }
              
              // 启用 RTSP 流按钮
              document.getElementById('startRtsp').disabled = false;
              
              // 订阅现有的发布者（包括可能的 RTSP 流）
              if(msg["publishers"] !== undefined && msg["publishers"] !== null) {
                var list = msg["publishers"];
                for(var f in list) {
                  var id = list[f]["id"];
                  var display = list[f]["display"];
                  log('发现发布者: ' + display + ' (ID: ' + id + ')');
                  
                  // 检查是否是 RTSP 流
                  if(display.includes("RTSP") || id == rtspStreamId) {
                    log('发现 RTSP 流，开始订阅');
                    subscribeToRtspFeed(id, display);
                  } else {
                    newRemoteFeed(id, display);
                  }
                }
              }
            } else if(event === "event") {
              if(msg["publishers"] !== undefined && msg["publishers"] !== null) {
                var list = msg["publishers"];
                for(var f in list) {
                  var id = list[f]["id"];
                  var display = list[f]["display"];
                  log('新发布者加入: ' + display + ' (ID: ' + id + ')');
                  
                  // 检查是否是 RTSP 流
                  if(display.includes("RTSP") || id == rtspStreamId) {
                    log('新 RTSP 流加入，开始订阅');
                    subscribeToRtspFeed(id, display);
                  } else {
                    newRemoteFeed(id, display);
                  }
                }
              } else if(msg["leaving"] !== undefined && msg["leaving"] !== null) {
                var leaving = msg["leaving"];
                log('发布者离开: ' + leaving);
                // 清理对应的 feed
                cleanupFeed(leaving);
              }
            }
          }
          
          if(jsep !== undefined && jsep !== null) {
            log('处理 SDP: ' + jsep.type);
            videoroom.handleRemoteJsep({jsep: jsep});
          }
        },
        onlocalstream: function(stream) {
          log('本地流已准备');
        },
        onremotestream: function(stream) {
          // VideoRoom 中不会调用这个
        },
        oncleanup: function() {
          log('清理本地流');
          mystream = null;
        }
      });
    }

    // 加入房间
    function joinRoom() {
      var register = {
        "request": "join",
        "room": 1000,
        "ptype": "publisher",
        "display": "用户-" + Janus.randomString(6)
      };
      videoroom.send({"message": register});
    }

    // 没有摄像头时加入房间
    function joinRoomWithoutCamera() {
      var register = {
        "request": "join",
        "room": 1000,
        "ptype": "subscriber",
        "display": "观看者-" + Janus.randomString(6)
      };
      videoroom.send({"message": register});
    }

    // 发布本地流
    function publishOwnFeed(useAudio) {
      videoroom.createOffer({
        media: {
          audioRecv: false,
          videoRecv: false,
          audioSend: useAudio,
          videoSend: true
        },
        stream: mystream,
        success: function(jsep) {
          log('创建 offer 成功');
          var publish = { "request": "configure", "audio": useAudio, "video": true };
          videoroom.send({"message": publish, "jsep": jsep});
        },
        error: function(error) {
          log('创建 offer 失败: ' + error);
          updateStatus('发布失败: ' + error, 'error');
        }
      });
    }

    // 订阅 RTSP 流
    function subscribeToRtspFeed(id, display) {
      log('开始订阅 RTSP 流: ' + display + ' (ID: ' + id + ')');
      
      var remoteFeed = null;
      janus.attach({
        plugin: "janus.plugin.videoroom",
        opaqueId: opaqueId,
        success: function(pluginHandle) {
          remoteFeed = pluginHandle;
          remoteFeed.rfid = id;
          remoteFeed.rfdisplay = display;
          feeds[id] = remoteFeed;
          log('RTSP 订阅插件连接成功');
          
          var subscribe = { 
            "request": "join", 
            "room": 1000, 
            "ptype": "subscriber", 
            "feed": id,
            "streams": [
              { "feed": id, "mid": "video", "send": true }
            ]
          };
          remoteFeed.send({"message": subscribe});
        },
        error: function(error) {
          log('RTSP 订阅插件连接失败: ' + error);
        },
        onmessage: function(msg, jsep) {
          var event = msg["videoroom"];
          if(event != undefined && event != null) {
            if(event === "attached") {
              log('成功订阅 RTSP 流: ' + remoteFeed.rfdisplay);
              updateStatus('正在接收 RTSP 视频流...', 'info');
            }
          }
          if(jsep !== undefined && jsep !== null) {
            log('处理 RTSP 订阅 SDP: ' + jsep.type);
            remoteFeed.createAnswer({
              jsep: jsep,
              media: { audioSend: false, videoSend: false, audioRecv: false, videoRecv: true },
              success: function(jsep) {
                var body = { "request": "start", "room": 1000 };
                remoteFeed.send({"message": body, "jsep": jsep});
              },
              error: function(error) {
                log('RTSP 订阅应答失败: ' + error);
              }
            });
          }
        },
        onremotestream: function(stream) {
          log('收到 RTSP 视频流: ' + remoteFeed.rfdisplay);
          document.getElementById('rtspVideo').srcObject = stream;
          updateStatus('RTSP 视频流播放中', 'success');
          
          // 监听视频播放事件
          var video = document.getElementById('rtspVideo');
          video.onloadedmetadata = function() {
            log('RTSP 视频元数据加载完成: ' + video.videoWidth + 'x' + video.videoHeight);
          };
          video.onplay = function() {
            log('RTSP 视频开始播放');
          };
          video.onerror = function(e) {
            log('RTSP 视频播放错误: ' + e.message);
          };
        },
        oncleanup: function() {
          log('清理 RTSP 流: ' + remoteFeed.rfdisplay);
          document.getElementById('rtspVideo').srcObject = null;
        }
      });
    }

    // 订阅普通用户流
    function newRemoteFeed(id, display) {
      log('开始订阅用户流: ' + display + ' (ID: ' + id + ')');
      
      var remoteFeed = null;
      janus.attach({
        plugin: "janus.plugin.videoroom",
        opaqueId: opaqueId,
        success: function(pluginHandle) {
          remoteFeed = pluginHandle;
          remoteFeed.rfid = id;
          remoteFeed.rfdisplay = display;
          feeds[id] = remoteFeed;
          log('用户订阅插件连接成功');
          
          var subscribe = { 
            "request": "join", 
            "room": 1000, 
            "ptype": "subscriber", 
            "feed": id,
            "streams": [
              { "feed": id, "mid": "video", "send": true }
            ]
          };
          remoteFeed.send({"message": subscribe});
        },
        error: function(error) {
          log('用户订阅插件连接失败: ' + error);
        },
        onmessage: function(msg, jsep) {
          var event = msg["videoroom"];
          if(event != undefined && event != null) {
            if(event === "attached") {
              log('成功订阅用户: ' + remoteFeed.rfdisplay);
            }
          }
          if(jsep !== undefined && jsep !== null) {
            log('处理用户订阅 SDP: ' + jsep.type);
            remoteFeed.createAnswer({
              jsep: jsep,
              media: { audioSend: false, videoSend: false, audioRecv: true, videoRecv: true },
              success: function(jsep) {
                var body = { "request": "start", "room": 1000 };
                remoteFeed.send({"message": body, "jsep": jsep});
              },
              error: function(error) {
                log('用户订阅应答失败: ' + error);
              }
            });
          }
        },
        onremotestream: function(stream) {
          log('收到用户视频流: ' + remoteFeed.rfdisplay);
          document.getElementById('remoteVideo').srcObject = stream;
          updateStatus('正在播放用户视频: ' + remoteFeed.rfdisplay, 'success');
        },
        oncleanup: function() {
          log('清理用户流: ' + remoteFeed.rfdisplay);
          document.getElementById('remoteVideo').srcObject = null;
        }
      });
    }

    // 启动 RTSP 流（通过 Streaming 插件）
    function startRtspStream() {
      log('正在启动 RTSP 流...');
      document.getElementById('startRtsp').disabled = true;
      
      janus.attach({
        plugin: "janus.plugin.streaming",
        opaqueId: opaqueId + "-streaming",
        success: function(pluginHandle) {
          streaming = pluginHandle;
          log('Streaming 插件连接成功');
          
          // 启动 RTSP 流
          var body = { "request": "start", "id": rtspStreamId };
          streaming.send({"message": body});
        },
        error: function(error) {
          log('Streaming 插件连接失败: ' + error);
          document.getElementById('startRtsp').disabled = false;
        },
        onmessage: function(msg, jsep) {
          log('Streaming 消息: ' + JSON.stringify(msg));
          
          if(msg["result"] && msg["result"]["status"] === "starting") {
            log('RTSP 流正在启动...');
          } else if(msg["result"] && msg["result"]["status"] === "started") {
            log('RTSP 流已启动');
            updateStatus('RTSP 流已启动，等待在房间中出现...', 'success');
          }
        }
      });
    }

    // 清理 feed
    function cleanupFeed(feedId) {
      if(feeds[feedId]) {
        feeds[feedId].detach();
        delete feeds[feedId];
      }
    }

    // 停止函数
    function stop() {
      document.getElementById('stop').disabled = true;
      document.getElementById('start').disabled = false;
      document.getElementById('startRtsp').disabled = true;
      
      if(janus != null) {
        janus.destroy();
        janus = null;
      }
      
      if(mystream != null) {
        mystream.getTracks().forEach(function(track) {
          track.stop();
        });
        mystream = null;
      }
      
      document.getElementById('localVideo').srcObject = null;
      document.getElementById('remoteVideo').srcObject = null;
      document.getElementById('rtspVideo').srcObject = null;
      
      feeds = [];
      updateStatus('已停止', 'info');
    }

    // 页面卸载时清理
    window.addEventListener('beforeunload', function() {
      if(janus != null) {
        janus.destroy();
      }
    });
  </script>
</body>
</html>
