#!/bin/bash

echo "=== WebRTC 网络包分析脚本 ==="
echo "时间: $(date)"
echo ""

CLIENT_IP="***********"
SERVER_IP="*************"

# 1. 实时抓包分析
echo "1. 开始实时抓包分析"
echo "===================="
echo "监控以下流量:"
echo "- WebSocket (8188)"
echo "- STUN/TURN (3478)"
echo "- RTP 媒体流 (20000-40000)"
echo "- DTLS 握手"
echo ""

# 创建抓包过滤器
FILTER="host $CLIENT_IP and ("
FILTER+="port 8188 or "           # WebSocket
FILTER+="port 3478 or "           # STUN/TURN
FILTER+="(udp and portrange 20000-40000)"  # RTP
FILTER+=")"

echo "抓包过滤器: $FILTER"
echo ""

# 2. 分析特定协议
echo "2. 协议分析"
echo "==========="

# 检查 STUN 包
echo "STUN 协议分析 (10秒):"
timeout 10 tcpdump -i any -n "$FILTER" 2>/dev/null | while read line; do
    if echo "$line" | grep -q "UDP.*3478"; then
        echo "STUN: $line"
    fi
done &

# 检查 RTP 包
echo ""
echo "RTP 协议分析 (10秒):"
timeout 10 tcpdump -i any -n "$FILTER" 2>/dev/null | while read line; do
    if echo "$line" | grep -qE "UDP.*:2[0-9][0-9][0-9][0-9]"; then
        echo "RTP: $line"
    fi
done &

# 检查 WebSocket 包
echo ""
echo "WebSocket 协议分析 (10秒):"
timeout 10 tcpdump -i any -n "$FILTER" 2>/dev/null | while read line; do
    if echo "$line" | grep -q "8188"; then
        echo "WS: $line"
    fi
done &

wait

echo ""
echo "3. 详细包分析"
echo "============="

# 创建详细抓包文件
PCAP_FILE="/tmp/webrtc_debug_$(date +%H%M%S).pcap"
echo "开始详细抓包，保存到: $PCAP_FILE"
echo "请在另一个终端启动 WebRTC 测试，然后按 Ctrl+C 停止抓包"

tcpdump -i any -w "$PCAP_FILE" "$FILTER" &
TCPDUMP_PID=$!

# 等待用户停止
trap "kill $TCPDUMP_PID 2>/dev/null; echo ''; echo '抓包已停止'" INT

echo "抓包进行中... (按 Ctrl+C 停止)"
wait $TCPDUMP_PID 2>/dev/null

echo ""
echo "4. 包文件分析"
echo "============="

if [ -f "$PCAP_FILE" ]; then
    echo "分析抓包文件: $PCAP_FILE"
    
    # 统计包数量
    echo ""
    echo "包统计:"
    echo "总包数: $(tcpdump -r "$PCAP_FILE" 2>/dev/null | wc -l)"
    echo "STUN包: $(tcpdump -r "$PCAP_FILE" 'port 3478' 2>/dev/null | wc -l)"
    echo "WebSocket包: $(tcpdump -r "$PCAP_FILE" 'port 8188' 2>/dev/null | wc -l)"
    echo "RTP包: $(tcpdump -r "$PCAP_FILE" 'udp and portrange 20000-40000' 2>/dev/null | wc -l)"
    
    # 分析流向
    echo ""
    echo "流向分析:"
    echo "客户端->服务器:"
    tcpdump -r "$PCAP_FILE" "src $CLIENT_IP and dst $SERVER_IP" 2>/dev/null | head -5
    echo ""
    echo "服务器->客户端:"
    tcpdump -r "$PCAP_FILE" "src $SERVER_IP and dst $CLIENT_IP" 2>/dev/null | head -5
    
    # 检查 DTLS 握手
    echo ""
    echo "DTLS 握手分析:"
    tcpdump -r "$PCAP_FILE" -v 'udp and portrange 20000-40000' 2>/dev/null | grep -E "DTLS|handshake" | head -10
    
    # 检查包大小分布
    echo ""
    echo "包大小分析:"
    tcpdump -r "$PCAP_FILE" -q 2>/dev/null | awk '{print $NF}' | grep -E '^[0-9]+$' | sort -n | uniq -c | tail -10
    
    echo ""
    echo "抓包文件已保存: $PCAP_FILE"
    echo "可以使用 Wireshark 打开进行详细分析"
else
    echo "未生成抓包文件"
fi

echo ""
echo "=== 分析完成 ==="
