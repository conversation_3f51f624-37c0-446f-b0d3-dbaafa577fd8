# 简化的Janus配置文件 - 用于快速启动和测试
general: {
	configs_folder = "/opt/janus/etc/janus"
	plugins_folder = "/opt/janus/lib/janus/plugins"
	transports_folder = "/opt/janus/lib/janus/transports"
	events_folder = "/opt/janus/lib/janus/events"
	loggers_folder = "/opt/janus/lib/janus/loggers"
	
	debug_level = 4
	admin_secret = "janusoverlord"
	
	protected_folders = [
		"/bin", "/boot", "/dev", "/etc", "/initrd", "/lib", "/lib32", "/lib64",
		"/proc", "/sbin", "/sys", "/usr", "/var",
		"/opt/janus/bin", "/opt/janus/etc", "/opt/janus/include", 
		"/opt/janus/lib", "/opt/janus/lib32", "/opt/janus/lib64", "/opt/janus/sbin"
	]
}

certificates: {
	# 使用自签名证书
}

media: {
	rtp_port_range = "20000-40000"
}

nat: {
	# 不使用STUN/TURN服务器，先测试基本功能
	nice_debug = false
	full_trickle = true
	ice_nomination = "aggressive"
	
	# 配置公网IP映射
	nat_1_1_mapping = "*************"
	keep_private_host = false
	
	# 强制使用指定的网络接口
	ice_enforce_list = "*************"
	ice_ignore_list = "vmnet"
}

plugins: {
	# 启用所有插件
}

transports: {
	# 启用所有传输
}

loggers: {
	# 启用所有日志记录器
}

events: {
	# 禁用事件处理器以简化配置
}
