#!/bin/bash

echo "=== 网络连接测试 ==="
echo "时间: $(date)"
echo ""

CLIENT_IP="***********"
SERVER_IP="*************"

# 1. 基本连通性测试
echo "1. 基本连通性测试"
echo "================"
echo "从服务器 ping 客户端:"
if ping -c 3 $CLIENT_IP >/dev/null 2>&1; then
    echo "✅ 可以 ping 通客户端 $CLIENT_IP"
else
    echo "❌ 无法 ping 通客户端 $CLIENT_IP"
fi

echo ""
echo "测试客户端到服务器的连接:"
echo "请在客户端 ($CLIENT_IP) 运行: ping $SERVER_IP"
echo ""

# 2. 端口连通性测试
echo "2. 端口连通性测试"
echo "================"
PORTS=(8188 8088 3478 3479)
for port in "${PORTS[@]}"; do
    if nc -z -w3 $SERVER_IP $port 2>/dev/null; then
        echo "✅ 端口 $port 可达"
    else
        echo "❌ 端口 $port 不可达"
    fi
done
echo ""

# 3. RTP 端口范围测试
echo "3. RTP 端口范围测试"
echo "=================="
echo "测试 RTP 端口范围 (20000-20010):"
RTP_REACHABLE=0
for port in {20000..20010}; do
    if nc -z -w1 $SERVER_IP $port 2>/dev/null; then
        echo "✅ RTP 端口 $port 可达"
        ((RTP_REACHABLE++))
    fi
done

if [ $RTP_REACHABLE -eq 0 ]; then
    echo "⚠️  没有 RTP 端口可达，这可能是防火墙问题"
else
    echo "✅ 有 $RTP_REACHABLE 个 RTP 端口可达"
fi
echo ""

# 4. TURN 服务器测试
echo "4. TURN 服务器测试"
echo "================"
if command -v turnutils_uclient >/dev/null; then
    echo "使用 turnutils_uclient 测试 TURN 服务器:"
    timeout 10 turnutils_uclient -t -u hhzt -w hhzt20130403 $SERVER_IP
else
    echo "⚠️  turnutils_uclient 未安装，跳过 TURN 测试"
fi
echo ""

# 5. 防火墙规则检查
echo "5. 防火墙规则检查"
echo "================"
if command -v iptables >/dev/null; then
    echo "iptables 规则 (INPUT 链):"
    iptables -L INPUT -n | grep -E "ACCEPT|DROP|REJECT" | head -10
    echo ""
    echo "iptables 规则 (FORWARD 链):"
    iptables -L FORWARD -n | grep -E "ACCEPT|DROP|REJECT" | head -5
else
    echo "⚠️  iptables 未找到"
fi
echo ""

# 6. 网络接口检查
echo "6. 网络接口检查"
echo "================"
echo "活跃的网络接口:"
ip addr show | grep -E "^[0-9]+:|inet " | grep -v "127.0.0.1"
echo ""

# 7. 路由表检查
echo "7. 路由表检查"
echo "============="
echo "默认路由:"
ip route | grep default
echo ""
echo "到客户端的路由:"
ip route get $CLIENT_IP 2>/dev/null || echo "无法获取到客户端的路由"
echo ""

# 8. NAT 检查
echo "8. NAT 检查"
echo "==========="
echo "检查是否启用了 IP 转发:"
if [ "$(cat /proc/sys/net/ipv4/ip_forward)" = "1" ]; then
    echo "✅ IP 转发已启用"
else
    echo "⚠️  IP 转发未启用"
fi

echo ""
echo "检查 NAT 规则:"
if iptables -t nat -L -n | grep -q "MASQUERADE\|SNAT"; then
    echo "✅ 发现 NAT 规则"
    iptables -t nat -L -n | grep -E "MASQUERADE|SNAT" | head -3
else
    echo "⚠️  未发现 NAT 规则"
fi
echo ""

echo "=== 网络测试完成 ==="
echo ""
echo "建议检查项目:"
echo "1. 确保客户端可以访问服务器的所有必要端口"
echo "2. 检查防火墙是否阻止了 RTP 端口范围"
echo "3. 确保 TURN 服务器正常工作"
echo "4. 检查 NAT 配置是否正确"
