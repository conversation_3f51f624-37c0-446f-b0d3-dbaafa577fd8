<!DOCTYPE html>
<html>
<head>
  <title>192.168.1.2 专用测试页面</title>
  <style>
    body { font-family: Arial, sans-serif; margin: 20px; }
    .status { padding: 10px; margin: 10px 0; border-radius: 5px; }
    .success { background-color: #d4edda; color: #155724; }
    .error { background-color: #f8d7da; color: #721c24; }
    .info { background-color: #d1ecf1; color: #0c5460; }
    .warning { background-color: #fff3cd; color: #856404; }
    video { width: 320px; height: 240px; border: 1px solid #ccc; margin: 10px; }
    button { padding: 10px 20px; margin: 5px; font-size: 16px; }
    #log { height: 400px; overflow-y: scroll; border: 1px solid #ccc; padding: 10px; font-family: monospace; font-size: 12px; }
  </style>
</head>
<body>
  <h1>192.168.1.2 专用 WebRTC 测试</h1>
  
  <div id="status" class="status info">准备开始测试...</div>
  
  <div>
    <button onclick="startTest()">开始测试</button>
    <button onclick="clearLog()">清除日志</button>
    <button onclick="testNetworkConnectivity()">测试网络连接</button>
  </div>
  
  <div>
    <h3>本地视频</h3>
    <video id="localVideo" autoplay muted playsinline></video>
    
    <h3>远程视频</h3>
    <video id="remoteVideo" autoplay playsinline></video>
  </div>
  
  <div>
    <h3>调试日志</h3>
    <div id="log"></div>
  </div>

  <script src="adapter-latest.js"></script>
  <script src="janus.js"></script>
  
  <script>
    let janus = null;
    let videoroom = null;
    let localStream = null;
    let remoteFeed = null;
    
    function log(message) {
      const timestamp = new Date().toLocaleTimeString();
      const logDiv = document.getElementById('log');
      logDiv.innerHTML += `[${timestamp}] ${message}<br>`;
      logDiv.scrollTop = logDiv.scrollHeight;
      console.log(`[${timestamp}] ${message}`);
    }
    
    function updateStatus(message, type = 'info') {
      const statusDiv = document.getElementById('status');
      statusDiv.textContent = message;
      statusDiv.className = `status ${type}`;
      log(`状态: ${message}`);
    }
    
    function clearLog() {
      document.getElementById('log').innerHTML = '';
    }
    
    function testNetworkConnectivity() {
      log('🔍 测试网络连接...');
      
      // 测试 WebSocket 连接
      const ws = new WebSocket('ws://*************:8188/');
      ws.onopen = function() {
        log('✅ WebSocket 连接成功');
        ws.close();
      };
      ws.onerror = function() {
        log('❌ WebSocket 连接失败');
      };
      
      // 测试 HTTP 连接
      fetch('http://*************:8088/janus/info')
        .then(response => response.json())
        .then(data => {
          log('✅ HTTP 连接成功');
          log(`Janus 信息: ${JSON.stringify(data).substring(0, 100)}...`);
        })
        .catch(error => {
          log('❌ HTTP 连接失败: ' + error.message);
        });
    }
    
    async function startTest() {
      try {
        updateStatus('开始测试...', 'info');
        
        // 1. 获取摄像头
        log('📹 获取摄像头...');
        localStream = await navigator.mediaDevices.getUserMedia({
          video: { width: 640, height: 480 },
          audio: false
        });
        
        document.getElementById('localVideo').srcObject = localStream;
        log('✅ 摄像头获取成功');
        
        // 2. 初始化 Janus
        log('🔧 初始化 Janus...');
        Janus.init({
          debug: "all",
          callback: function() {
            log('✅ Janus 初始化成功');
            connectToJanus();
          }
        });
        
      } catch (error) {
        log('❌ 测试失败: ' + error.message);
        updateStatus('测试失败: ' + error.message, 'error');
      }
    }
    
    function connectToJanus() {
      janus = new Janus({
        server: "ws://*************:8188/",
        // 针对 192.168.1.2 的特殊配置
        iceServers: [
          { urls: "stun:stun.l.google.com:19302" },
          { urls: "stun:stun1.l.google.com:19302" },
          { urls: "turn:*************:3478", username: "hhzt", credential: "hhzt20130403" }
        ],
        // 不强制使用 relay，允许多种连接方式
        iceTransportPolicy: "all",
        iceCandidatePoolSize: 10,
        bundlePolicy: "max-bundle",
        rtcpMuxPolicy: "require",
        success: function() {
          log('✅ Janus 连接成功');
          updateStatus('Janus 连接成功', 'success');
          attachVideoRoom();
        },
        error: function(error) {
          log('❌ Janus 连接失败: ' + error);
          updateStatus('Janus 连接失败: ' + error, 'error');
        },
        destroyed: function() {
          log('⚠️ Janus 连接已断开');
        }
      });
    }
    
    function attachVideoRoom() {
      janus.attach({
        plugin: "janus.plugin.videoroom",
        opaqueId: "client-192-168-1-2-" + Janus.randomString(12),
        success: function(pluginHandle) {
          videoroom = pluginHandle;
          log('✅ VideoRoom 插件连接成功');
          
          // 加入房间
          const register = {
            request: "join",
            room: 1000,
            ptype: "publisher",
            display: "客户端-192.168.1.2",
            pin: ""
          };
          
          videoroom.send({ message: register });
          log('📤 发送加入房间请求');
        },
        error: function(error) {
          log('❌ VideoRoom 连接失败: ' + error);
          updateStatus('VideoRoom 连接失败: ' + error, 'error');
        },
        iceState: function(state) {
          log(`🧊 ICE 状态: ${state}`);
          if (state === 'connected') {
            updateStatus('ICE 连接成功', 'success');
          } else if (state === 'failed') {
            updateStatus('ICE 连接失败', 'error');
          }
        },
        mediaState: function(medium, on) {
          log(`📺 媒体状态: ${medium} ${on ? '开启' : '关闭'}`);
        },
        webrtcState: function(on) {
          log(`🔗 WebRTC 状态: ${on ? '已连接' : '已断开'}`);
          if (!on && videoroom) {
            log('⚠️ WebRTC 断开，尝试重新连接...');
            setTimeout(() => {
              if (videoroom) {
                videoroom.hangup();
                setTimeout(attachVideoRoom, 2000);
              }
            }, 1000);
          }
        },
        onmessage: function(msg, jsep) {
          log('📨 收到消息: ' + JSON.stringify(msg));
          
          if (msg["videoroom"] === "joined") {
            log('✅ 已加入房间');
            updateStatus('已加入房间，开始发布流...', 'success');
            
            // 发布本地流
            videoroom.createOffer({
              media: {
                audioRecv: false,
                videoRecv: false,
                audioSend: false,
                videoSend: true,
                data: false
              },
              stream: localStream,
              success: function(jsep) {
                log('✅ 创建 offer 成功');
                const publish = {
                  request: "configure",
                  audio: false,
                  video: true,
                  bitrate: 1000000  // 1 Mbps
                };
                videoroom.send({ message: publish, jsep: jsep });
              },
              error: function(error) {
                log('❌ 创建 offer 失败: ' + error);
              }
            });
            
            // 检查是否有其他发布者
            if (msg["publishers"]) {
              msg["publishers"].forEach(publisher => {
                if (publisher["id"] !== msg["id"]) {
                  log(`🔍 发现其他发布者: ${publisher["display"]} (${publisher["id"]})`);
                  subscribeToFeed(publisher["id"], publisher["display"]);
                }
              });
            }
          } else if (msg["videoroom"] === "event") {
            if (msg["configured"] === "ok") {
              log('✅ 发布配置成功');
              updateStatus('本地流发布成功', 'success');
            }
            
            if (msg["publishers"]) {
              msg["publishers"].forEach(publisher => {
                log(`🔍 新发布者: ${publisher["display"]} (${publisher["id"]})`);
                subscribeToFeed(publisher["id"], publisher["display"]);
              });
            }
          }
          
          if (jsep) {
            log('📥 处理 SDP: ' + jsep.type);
            videoroom.handleRemoteJsep({ jsep: jsep });
          }
        }
      });
    }
    
    function subscribeToFeed(id, display) {
      log(`🔄 开始订阅 ${display} (ID: ${id})`);
      
      janus.attach({
        plugin: "janus.plugin.videoroom",
        opaqueId: "subscriber-192-168-1-2-" + Janus.randomString(12),
        success: function(pluginHandle) {
          remoteFeed = pluginHandle;
          log('✅ 订阅插件连接成功');
          
          const subscribe = {
            request: "join",
            room: 1000,
            ptype: "subscriber",
            feed: id,
            private_id: Math.floor(Math.random() * 1000000000),
            offer_audio: false,
            offer_video: true,
            pin: "",
            streams: [{ feed: id, mid: "0", send: true }]
          };
          
          remoteFeed.send({ message: subscribe });
        },
        error: function(error) {
          log('❌ 订阅失败: ' + error);
        },
        iceState: function(state) {
          log(`🧊 订阅 ICE 状态: ${state}`);
        },
        webrtcState: function(on) {
          log(`🔗 订阅 WebRTC 状态: ${on ? '已连接' : '已断开'}`);
        },
        onremotetrack: function(track, mid, on) {
          log(`🎬 收到远程轨道: ${track.kind}, mid: ${mid}, on: ${on}`);
          
          if (track.kind === 'video' && on) {
            const remoteVideo = document.getElementById('remoteVideo');
            const stream = new MediaStream([track]);
            remoteVideo.srcObject = stream;
            
            remoteVideo.onloadedmetadata = function() {
              log('✅ 远程视频元数据加载完成');
              remoteVideo.play().then(() => {
                log('✅ 远程视频开始播放');
                updateStatus('视频播放成功！', 'success');
              }).catch(e => {
                log('❌ 远程视频播放失败: ' + e.message);
              });
            };
          }
        },
        onmessage: function(msg, jsep) {
          log('📨 订阅消息: ' + JSON.stringify(msg));
          
          if (jsep) {
            log('📥 处理订阅 SDP: ' + jsep.type);
            remoteFeed.createAnswer({
              jsep: jsep,
              media: { audioSend: false, videoSend: false },
              success: function(jsep) {
                log('✅ 订阅应答创建成功');
                const body = { request: "start", room: 1000 };
                remoteFeed.send({ message: body, jsep: jsep });
              },
              error: function(error) {
                log('❌ 订阅应答创建失败: ' + error);
              }
            });
          }
        }
      });
    }
  </script>
</body>
</html>
