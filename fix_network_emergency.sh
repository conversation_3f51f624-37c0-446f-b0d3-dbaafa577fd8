#!/bin/bash

echo "=== 紧急网络修复 ==="
echo "时间: $(date)"
echo ""

# 1. 检查当前防火墙状态
echo "1. 检查防火墙状态"
echo "=================="
if systemctl is-active firewalld >/dev/null 2>&1; then
    echo "firewalld 正在运行"
    firewall-cmd --list-all
    echo ""
    echo "临时开放所有必要端口..."
    firewall-cmd --add-port=8188/tcp --timeout=3600
    firewall-cmd --add-port=8088/tcp --timeout=3600
    firewall-cmd --add-port=3478/udp --timeout=3600
    firewall-cmd --add-port=3479/tcp --timeout=3600
    firewall-cmd --add-port=20000-40000/udp --timeout=3600
    echo "✅ 防火墙端口已临时开放"
elif systemctl is-active ufw >/dev/null 2>&1; then
    echo "ufw 正在运行"
    ufw status
    echo ""
    echo "临时开放端口..."
    ufw allow 8188/tcp
    ufw allow 8088/tcp
    ufw allow 3478/udp
    ufw allow 3479/tcp
    ufw allow 20000:40000/udp
    echo "✅ ufw 端口已开放"
else
    echo "检查 iptables..."
    iptables -L -n | head -20
fi
echo ""

# 2. 检查网络接口状态
echo "2. 检查网络接口"
echo "================"
echo "活跃接口:"
ip link show | grep -E "^[0-9]+:|state UP"
echo ""
echo "IP 地址:"
ip addr show | grep -E "inet.*60\.255\.197\.32"
echo ""

# 3. 检查路由表
echo "3. 检查路由表"
echo "============="
echo "默认路由:"
ip route show default
echo ""
echo "本地路由:"
ip route show | grep "60.255.197"
echo ""

# 4. 测试网络连通性
echo "4. 测试网络连通性"
echo "=================="
echo "测试到网关的连通性:"
GATEWAY=$(ip route show default | awk '{print $3}' | head -1)
if [ -n "$GATEWAY" ]; then
    if ping -c 3 "$GATEWAY" >/dev/null 2>&1; then
        echo "✅ 网关 $GATEWAY 可达"
    else
        echo "❌ 网关 $GATEWAY 不可达"
    fi
else
    echo "⚠️  未找到默认网关"
fi
echo ""

# 5. 检查端口监听
echo "5. 检查端口监听"
echo "================"
echo "Janus 相关端口:"
netstat -tlnp | grep -E ":8188|:8088"
echo ""
echo "RTP 端口范围 (前10个):"
netstat -unlp | grep -E ":200[0-9][0-9]" | head -10
echo ""

# 6. 测试出站连接
echo "6. 测试出站连接"
echo "================"
echo "测试 DNS 解析:"
if nslookup google.com >/dev/null 2>&1; then
    echo "✅ DNS 解析正常"
else
    echo "❌ DNS 解析失败"
fi

echo ""
echo "测试外部连接:"
if curl -s --connect-timeout 5 http://www.baidu.com >/dev/null; then
    echo "✅ 外部连接正常"
else
    echo "❌ 外部连接失败"
fi
echo ""

# 7. 检查系统资源
echo "7. 检查系统资源"
echo "================"
echo "内存使用:"
free -h | head -2
echo ""
echo "磁盘使用:"
df -h | grep -E "/$|/opt"
echo ""
echo "网络统计:"
cat /proc/net/dev | grep -E "eno1|eth0" | head -2
echo ""

# 8. 临时网络修复尝试
echo "8. 临时网络修复"
echo "================"
echo "刷新 ARP 表..."
ip neigh flush all

echo "重置网络统计..."
echo 1 > /proc/sys/net/ipv4/route/flush 2>/dev/null || true

echo "检查 IP 转发..."
if [ "$(cat /proc/sys/net/ipv4/ip_forward)" != "1" ]; then
    echo "启用 IP 转发..."
    echo 1 > /proc/sys/net/ipv4/ip_forward
    echo "✅ IP 转发已启用"
else
    echo "✅ IP 转发已启用"
fi

echo ""
echo "=== 修复完成 ==="
echo ""
echo "建议操作:"
echo "1. 重启 Janus 服务"
echo "2. 检查云服务器安全组设置"
echo "3. 确认客户端网络环境"
echo "4. 考虑使用 TURN 中继模式"
