<!DOCTYPE html>
<html>
<head>
  <title>WebRTC 环境诊断</title>
  <style>
    body { font-family: Arial, sans-serif; margin: 20px; }
    .test-section { margin: 20px 0; padding: 15px; border: 1px solid #ccc; border-radius: 5px; }
    .success { background-color: #d4edda; }
    .error { background-color: #f8d7da; }
    .warning { background-color: #fff3cd; }
    button { padding: 10px 20px; margin: 5px; }
    #results { height: 400px; overflow-y: scroll; border: 1px solid #ccc; padding: 10px; font-family: monospace; font-size: 12px; }
  </style>
</head>
<body>
  <h1>192.168.1.2 WebRTC 环境诊断</h1>
  
  <div class="test-section">
    <h3>系统信息</h3>
    <div id="systemInfo"></div>
  </div>
  
  <div class="test-section">
    <h3>测试控制</h3>
    <button onclick="runAllTests()">运行所有测试</button>
    <button onclick="testWebRTCBasics()">测试 WebRTC 基础</button>
    <button onclick="testICEGathering()">测试 ICE 收集</button>
    <button onclick="testPeerConnection()">测试对等连接</button>
    <button onclick="clearResults()">清除结果</button>
  </div>
  
  <div class="test-section">
    <h3>测试结果</h3>
    <div id="results"></div>
  </div>

  <script>
    function log(message, type = 'info') {
      const timestamp = new Date().toLocaleTimeString();
      const results = document.getElementById('results');
      const color = type === 'error' ? 'red' : type === 'success' ? 'green' : type === 'warning' ? 'orange' : 'black';
      results.innerHTML += `<span style="color: ${color}">[${timestamp}] ${message}</span><br>`;
      results.scrollTop = results.scrollHeight;
      console.log(message);
    }
    
    function clearResults() {
      document.getElementById('results').innerHTML = '';
    }
    
    // 显示系统信息
    function showSystemInfo() {
      const info = document.getElementById('systemInfo');
      info.innerHTML = `
        <p><strong>用户代理:</strong> ${navigator.userAgent}</p>
        <p><strong>平台:</strong> ${navigator.platform}</p>
        <p><strong>语言:</strong> ${navigator.language}</p>
        <p><strong>在线状态:</strong> ${navigator.onLine ? '在线' : '离线'}</p>
        <p><strong>连接类型:</strong> ${navigator.connection ? navigator.connection.effectiveType : '未知'}</p>
        <p><strong>WebRTC 支持:</strong> ${window.RTCPeerConnection ? '支持' : '不支持'}</p>
        <p><strong>getUserMedia 支持:</strong> ${navigator.mediaDevices && navigator.mediaDevices.getUserMedia ? '支持' : '不支持'}</p>
      `;
    }
    
    async function runAllTests() {
      log('🔍 开始完整的 WebRTC 环境诊断', 'info');
      clearResults();
      
      await testWebRTCBasics();
      await new Promise(resolve => setTimeout(resolve, 1000));
      
      await testICEGathering();
      await new Promise(resolve => setTimeout(resolve, 1000));
      
      await testPeerConnection();
      
      log('✅ 所有测试完成', 'success');
    }
    
    async function testWebRTCBasics() {
      log('📋 测试 WebRTC 基础功能...', 'info');
      
      // 检查 WebRTC API
      if (!window.RTCPeerConnection) {
        log('❌ RTCPeerConnection 不支持', 'error');
        return;
      }
      log('✅ RTCPeerConnection 支持', 'success');
      
      // 检查 getUserMedia
      if (!navigator.mediaDevices || !navigator.mediaDevices.getUserMedia) {
        log('❌ getUserMedia 不支持', 'error');
        return;
      }
      log('✅ getUserMedia 支持', 'success');
      
      // 测试摄像头访问
      try {
        const stream = await navigator.mediaDevices.getUserMedia({ video: true, audio: false });
        log('✅ 摄像头访问成功', 'success');
        
        const videoTrack = stream.getVideoTracks()[0];
        const settings = videoTrack.getSettings();
        log(`📹 视频设置: ${settings.width}x${settings.height} @${settings.frameRate}fps`, 'info');
        
        stream.getTracks().forEach(track => track.stop());
      } catch (error) {
        log(`❌ 摄像头访问失败: ${error.message}`, 'error');
      }
      
      // 检查编解码器支持
      const pc = new RTCPeerConnection();
      try {
        const transceivers = pc.getTransceivers();
        log(`📊 支持的收发器数量: ${transceivers.length}`, 'info');
        
        // 检查 VP8 支持
        if (RTCRtpSender.getCapabilities) {
          const videoCapabilities = RTCRtpSender.getCapabilities('video');
          const vp8Support = videoCapabilities.codecs.some(codec => codec.mimeType.includes('VP8'));
          log(`🎥 VP8 编解码器支持: ${vp8Support ? '是' : '否'}`, vp8Support ? 'success' : 'warning');
        }
      } catch (error) {
        log(`⚠️ 编解码器检查失败: ${error.message}`, 'warning');
      }
      pc.close();
    }
    
    async function testICEGathering() {
      log('🧊 测试 ICE 候选者收集...', 'info');
      
      const stunServers = [
        'stun:stun.l.google.com:19302',
        'stun:stun1.l.google.com:19302',
        'stun:*************:3478'
      ];
      
      for (const stunUrl of stunServers) {
        await testSingleSTUNServer(stunUrl);
      }
    }
    
    function testSingleSTUNServer(stunUrl) {
      return new Promise((resolve) => {
        log(`🔍 测试 STUN 服务器: ${stunUrl}`, 'info');
        
        const pc = new RTCPeerConnection({
          iceServers: [{ urls: stunUrl }]
        });
        
        let candidateCount = 0;
        let hasPublicIP = false;
        
        const timeout = setTimeout(() => {
          log(`⏰ ${stunUrl} 测试超时`, 'warning');
          pc.close();
          resolve();
        }, 10000);
        
        pc.onicecandidate = function(event) {
          if (event.candidate) {
            candidateCount++;
            const candidate = event.candidate.candidate;
            log(`  候选者 ${candidateCount}: ${candidate.substring(0, 60)}...`, 'info');
            
            if (candidate.includes('srflx')) {
              hasPublicIP = true;
              const publicIP = candidate.split(' ')[4];
              log(`✅ ${stunUrl} 获得公网 IP: ${publicIP}`, 'success');
            }
          } else {
            clearTimeout(timeout);
            log(`📊 ${stunUrl} 收集完成: ${candidateCount} 个候选者, 公网IP: ${hasPublicIP ? '是' : '否'}`, hasPublicIP ? 'success' : 'warning');
            pc.close();
            resolve();
          }
        };
        
        pc.onicegatheringstatechange = function() {
          log(`  ICE 收集状态: ${pc.iceGatheringState}`, 'info');
        };
        
        // 创建 offer 触发 ICE 收集
        pc.createOffer().then(offer => {
          return pc.setLocalDescription(offer);
        }).catch(error => {
          log(`❌ ${stunUrl} 创建 offer 失败: ${error.message}`, 'error');
          clearTimeout(timeout);
          pc.close();
          resolve();
        });
      });
    }
    
    async function testPeerConnection() {
      log('🔗 测试对等连接建立...', 'info');
      
      const pc1 = new RTCPeerConnection({
        iceServers: [{ urls: 'stun:stun.l.google.com:19302' }]
      });
      
      const pc2 = new RTCPeerConnection({
        iceServers: [{ urls: 'stun:stun.l.google.com:19302' }]
      });
      
      // 监听连接状态
      pc1.onconnectionstatechange = function() {
        log(`PC1 连接状态: ${pc1.connectionState}`, 'info');
      };
      
      pc2.onconnectionstatechange = function() {
        log(`PC2 连接状态: ${pc2.connectionState}`, 'info');
      };
      
      pc1.oniceconnectionstatechange = function() {
        log(`PC1 ICE 状态: ${pc1.iceConnectionState}`, 'info');
      };
      
      pc2.oniceconnectionstatechange = function() {
        log(`PC2 ICE 状态: ${pc2.iceConnectionState}`, 'info');
      };
      
      // ICE 候选者交换
      pc1.onicecandidate = function(event) {
        if (event.candidate) {
          pc2.addIceCandidate(event.candidate).catch(e => {
            log(`❌ PC2 添加候选者失败: ${e.message}`, 'error');
          });
        }
      };
      
      pc2.onicecandidate = function(event) {
        if (event.candidate) {
          pc1.addIceCandidate(event.candidate).catch(e => {
            log(`❌ PC1 添加候选者失败: ${e.message}`, 'error');
          });
        }
      };
      
      try {
        // 创建数据通道
        const dataChannel = pc1.createDataChannel('test');
        dataChannel.onopen = function() {
          log('✅ 数据通道打开', 'success');
          dataChannel.send('Hello WebRTC!');
        };
        
        pc2.ondatachannel = function(event) {
          const channel = event.channel;
          channel.onmessage = function(event) {
            log(`✅ 收到数据: ${event.data}`, 'success');
          };
        };
        
        // 创建 offer
        const offer = await pc1.createOffer();
        await pc1.setLocalDescription(offer);
        await pc2.setRemoteDescription(offer);
        
        // 创建 answer
        const answer = await pc2.createAnswer();
        await pc2.setLocalDescription(answer);
        await pc1.setRemoteDescription(answer);
        
        log('✅ SDP 交换完成', 'success');
        
        // 等待连接建立
        setTimeout(() => {
          log(`🔗 最终连接状态 - PC1: ${pc1.connectionState}, PC2: ${pc2.connectionState}`, 'info');
          pc1.close();
          pc2.close();
        }, 5000);
        
      } catch (error) {
        log(`❌ 对等连接测试失败: ${error.message}`, 'error');
        pc1.close();
        pc2.close();
      }
    }
    
    // 页面加载时显示系统信息
    window.onload = function() {
      showSystemInfo();
      log('🔧 WebRTC 环境诊断工具已准备就绪', 'info');
    };
  </script>
</body>
</html>
