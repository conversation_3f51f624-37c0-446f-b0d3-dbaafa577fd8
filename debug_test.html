<!DOCTYPE html>
<html>
<head>
    <title>WebRTC Debug Test</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .container { max-width: 1200px; margin: 0 auto; }
        .video-container { display: flex; gap: 20px; margin: 20px 0; }
        video { width: 300px; height: 200px; border: 1px solid #ccc; }
        .log { background: #f5f5f5; padding: 10px; height: 400px; overflow-y: scroll; font-family: monospace; font-size: 12px; }
        button { padding: 10px 20px; margin: 5px; font-size: 16px; }
        .status { padding: 10px; margin: 10px 0; border-radius: 5px; }
        .success { background: #d4edda; color: #155724; }
        .error { background: #f8d7da; color: #721c24; }
        .info { background: #d1ecf1; color: #0c5460; }
    </style>
</head>
<body>
    <div class="container">
        <h1>WebRTC 调试测试</h1>
        
        <div class="video-container">
            <div>
                <h3>本地视频</h3>
                <video id="localVideo" autoplay muted></video>
            </div>
            <div>
                <h3>远程视频</h3>
                <video id="remoteVideo" autoplay></video>
            </div>
        </div>
        
        <div>
            <button onclick="startTest()">开始调试测试</button>
            <button onclick="clearLog()">清空日志</button>
        </div>
        
        <div id="status" class="status info">准备开始测试...</div>
        
        <div class="log" id="log"></div>
    </div>

    <script src="https://webrtc.github.io/adapter/adapter-latest.js"></script>
    <script src="https://janus.conf.meetecho.com/janus.js"></script>
    
    <script>
        let janus = null;
        let videoroom = null;
        let localStream = null;
        let myId = null;
        
        function log(message) {
            const timestamp = new Date().toLocaleTimeString();
            const logDiv = document.getElementById('log');
            logDiv.innerHTML += `[${timestamp}] ${message}\n`;
            logDiv.scrollTop = logDiv.scrollHeight;
            console.log(message);
        }
        
        function updateStatus(message, type = 'info') {
            const statusDiv = document.getElementById('status');
            statusDiv.textContent = message;
            statusDiv.className = `status ${type}`;
        }
        
        function clearLog() {
            document.getElementById('log').innerHTML = '';
        }
        
        async function startTest() {
            try {
                log('🚀 开始调试测试...');
                updateStatus('正在获取摄像头...', 'info');
                
                // 获取本地媒体流
                localStream = await navigator.mediaDevices.getUserMedia({
                    video: { width: 640, height: 480 },
                    audio: true
                });
                
                document.getElementById('localVideo').srcObject = localStream;
                log('✅ 本地媒体流获取成功');
                log(`📹 视频轨道: ${localStream.getVideoTracks().length}`);
                log(`🎵 音频轨道: ${localStream.getAudioTracks().length}`);
                
                // 检查轨道状态
                localStream.getTracks().forEach(track => {
                    log(`🔍 轨道 ${track.kind}: enabled=${track.enabled}, muted=${track.muted}, readyState=${track.readyState}`);
                });
                
                updateStatus('初始化 Janus...', 'info');
                initJanus();
                
            } catch (error) {
                log(`❌ 错误: ${error.message}`);
                updateStatus(`错误: ${error.message}`, 'error');
            }
        }
        
        function initJanus() {
            Janus.init({
                debug: "all",
                callback: function() {
                    log('✅ Janus 初始化成功');
                    
                    janus = new Janus({
                        server: "ws://60.255.197.32:8188/",
                        success: function() {
                            log('✅ Janus 连接成功');
                            updateStatus('连接 VideoRoom...', 'info');
                            attachVideoRoom();
                        },
                        error: function(error) {
                            log(`❌ Janus 连接失败: ${error}`);
                            updateStatus(`Janus 连接失败: ${error}`, 'error');
                        },
                        destroyed: function() {
                            log('🔌 Janus 连接已断开');
                        }
                    });
                }
            });
        }
        
        function attachVideoRoom() {
            janus.attach({
                plugin: "janus.plugin.videoroom",
                opaqueId: "videoroomtest-" + Janus.randomString(12),
                success: function(pluginHandle) {
                    videoroom = pluginHandle;
                    log('✅ VideoRoom 插件连接成功');
                    
                    // 加入房间作为发布者
                    const register = {
                        request: "join",
                        room: 1234,
                        ptype: "publisher",
                        display: `调试用户-${Date.now()}`
                    };
                    
                    videoroom.send({ message: register });
                    log('📤 发送加入房间请求');
                },
                error: function(error) {
                    log(`❌ VideoRoom 连接失败: ${error}`);
                },
                onmessage: function(msg, jsep) {
                    log(`📨 收到消息: ${JSON.stringify(msg)}`);
                    
                    if (msg["videoroom"] === "joined") {
                        myId = msg["id"];
                        log(`✅ 已加入房间，ID: ${myId}`);
                        updateStatus('房间加入成功，开始发布...', 'success');
                        
                        // 立即发布媒体流
                        publishOwnFeed();
                    }
                    
                    if (jsep) {
                        log(`📥 处理 SDP: ${jsep.type}`);
                        videoroom.handleRemoteJsep({ jsep: jsep });
                    }
                },
                onlocalstream: function(stream) {
                    log('📤 本地流已设置');
                    // 本地流已经在 getUserMedia 时设置了
                },
                webrtcState: function(on) {
                    log(`🔗 WebRTC 状态: ${on ? '已连接' : '已断开'}`);
                    if (on) {
                        updateStatus('发布成功！等待其他用户加入...', 'success');
                    }
                },
                oncleanup: function() {
                    log('🧹 清理发布者连接');
                }
            });
        }
        
        function publishOwnFeed() {
            log('🎬 开始发布媒体流...');
            
            videoroom.createOffer({
                media: {
                    audioRecv: false,
                    videoRecv: false,
                    audioSend: true,
                    videoSend: true
                },
                stream: localStream,
                success: function(jsep) {
                    log('✅ Offer 创建成功');
                    const publish = { request: "configure", audio: true, video: true };
                    videoroom.send({ message: publish, jsep: jsep });
                },
                error: function(error) {
                    log(`❌ Offer 创建失败: ${error}`);
                }
            });
        }
    </script>
</body>
</html>
