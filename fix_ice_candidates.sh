#!/bin/bash

echo "=== 修复 ICE 候选者问题 ==="
echo "时间: $(date)"
echo ""

# 1. 检查服务器网络接口
echo "1. 检查服务器网络接口"
echo "===================="
echo "所有网络接口:"
ip addr show | grep -E "^[0-9]+:|inet "

echo ""
echo "活跃的网络接口:"
ip link show | grep -E "state UP"

echo ""
echo "路由表:"
ip route show

echo ""
echo "2. 检查当前 Janus ICE 配置"
echo "========================="
echo "ICE 强制列表:"
grep -E "ice_enforce_list" /opt/janus/etc/janus/janus.jcfg

echo ""
echo "ICE 忽略列表:"
grep -E "ice_ignore_list" /opt/janus/etc/janus/janus.jcfg

echo ""
echo "NAT 1:1 映射:"
grep -E "nat_1_1_mapping" /opt/janus/etc/janus/janus.jcfg

echo ""
echo "3. 修复 ICE 配置"
echo "================"

# 停止 Janus
if pgrep -f janus > /dev/null; then
    echo "停止 Janus..."
    pkill -f janus
    sleep 3
fi

# 备份配置
cp /opt/janus/etc/janus/janus.jcfg /opt/janus/etc/janus/janus.jcfg.backup.$(date +%H%M%S)
echo "✅ 配置已备份"

echo ""
echo "4. 应用新的 ICE 配置"
echo "=================="
echo "已移除 ice_enforce_list 限制"
echo "Janus 现在可以使用所有网络接口"

echo ""
echo "5. 重启 Janus"
echo "============="
cd /opt/janus

echo "启动 Janus..."
nohup bin/janus -c etc/janus/janus.jcfg > janus_fixed.log 2>&1 &

sleep 5

if pgrep -f janus > /dev/null; then
    echo "✅ Janus 启动成功"
    
    echo ""
    echo "检查端口监听:"
    netstat -tlnp | grep -E ":8188|:8088"
    
    echo ""
    echo "检查 ICE 候选者收集:"
    echo "等待 10 秒后检查日志..."
    sleep 10
    
    echo "最近的日志:"
    tail -20 janus_fixed.log | grep -E "ICE|candidate|interface"
    
else
    echo "❌ Janus 启动失败"
    echo "错误日志:"
    tail -20 janus_fixed.log
    exit 1
fi

echo ""
echo "6. 测试建议"
echo "==========="
echo "现在请在 192.168.1.2 上重新测试 WebRTC 连接"
echo ""
echo "预期结果:"
echo "- 不再出现 'Failed to add some remote candidates' 错误"
echo "- 192.168.1.2 应该能够接收到视频流"
echo "- 抓包应该能看到服务器向 192.168.1.2 发送的 RTP 包"

echo ""
echo "如果还有问题，请检查:"
echo "1. 192.168.1.2 到服务器的网络路径"
echo "2. 中间路由器的 NAT 配置"
echo "3. 防火墙设置"

echo ""
echo "=== 修复完成 ==="
