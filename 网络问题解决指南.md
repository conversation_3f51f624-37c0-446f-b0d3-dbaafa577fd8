# WebRTC网络连接问题解决指南

## 🔍 问题诊断结果

根据您的ICE连接测试结果，发现以下问题：

### 主要问题
1. **无法获取本地IP地址** - 表明客户端网络配置受限
2. **所有ICE候选者收集失败** - STUN/TURN服务器都无法连接
3. **P2P连接成功** - 说明在同一网络内直连正常

### 问题根源
这是典型的**企业网络/防火墙限制**问题，而不是Janus服务器配置问题。

## 🛠️ 解决方案

### 方案1：网络连通性测试（立即执行）

1. **使用网络连通性测试工具**：
   ```
   打开 network_connectivity_test.html
   依次执行所有测试
   ```

2. **检查具体的连接问题**：
   - 基础连通性测试
   - 服务器端口测试  
   - STUN服务器测试
   - Janus WebSocket测试

### 方案2：使用公共TURN服务器（临时解决）

1. **测试公共TURN版本**：
   ```
   打开 videoroom_public_turn_version.html
   这个版本使用免费的公共TURN服务器
   ```

2. **如果公共TURN也不工作**：
   - 说明是网络环境限制过严
   - 需要网络管理员协助

### 方案3：网络环境优化

#### 检查本地防火墙
```bash
# Windows防火墙
netsh advfirewall show allprofiles

# 临时关闭防火墙测试
netsh advfirewall set allprofiles state off
# 测试完成后记得重新开启
netsh advfirewall set allprofiles state on
```

#### 检查代理设置
1. 浏览器代理设置
2. 系统代理设置
3. 企业代理/VPN

#### 尝试不同网络
1. 手机热点
2. 家庭网络
3. 公共WiFi

### 方案4：服务器端优化

#### 1. 确保服务器防火墙配置正确
```bash
# 检查防火墙状态
systemctl status firewalld

# 开放所有必要端口
firewall-cmd --permanent --add-port=8188/tcp
firewall-cmd --permanent --add-port=3478/tcp
firewall-cmd --permanent --add-port=3478/udp
firewall-cmd --permanent --add-port=3479/tcp
firewall-cmd --permanent --add-port=20000-40000/udp
firewall-cmd --permanent --add-port=49152-65535/udp
firewall-cmd --reload
```

#### 2. 验证服务器端口监听
```bash
# 检查端口监听状态
netstat -tlnp | grep -E "(8188|3478|3479)"

# 检查进程状态
ps aux | grep -E "(janus|turnserver)"
```

#### 3. 测试服务器连通性
```bash
# 从服务器本地测试
curl -i -N -H "Connection: Upgrade" \
     -H "Upgrade: websocket" \
     -H "Sec-WebSocket-Key: test" \
     -H "Sec-WebSocket-Version: 13" \
     http://localhost:8188/
```

## 📋 分步骤诊断流程

### 第1步：基础网络测试
1. 打开 `network_connectivity_test.html`
2. 运行"基础连通性测试"
3. 如果公网连通性失败 → 检查网络连接
4. 如果DNS解析失败 → 检查DNS设置

### 第2步：服务器连接测试
1. 运行"测试服务器端口"
2. 如果WebSocket连接失败：
   - 检查服务器是否启动
   - 检查防火墙设置
   - 尝试直接访问 `http://*************:8188`

### 第3步：STUN/TURN测试
1. 运行"测试STUN服务器"
2. 如果所有STUN都失败：
   - 网络环境限制UDP流量
   - 需要使用TCP TURN或公共服务

### 第4步：替代方案测试
1. 打开 `videoroom_public_turn_version.html`
2. 测试是否能建立连接
3. 如果仍然失败 → 网络环境过于严格

## 🎯 针对性解决方案

### 如果是企业网络
1. **联系IT管理员**：
   - 开放WebRTC相关端口
   - 允许UDP流量
   - 配置防火墙白名单

2. **使用VPN**：
   - 连接到支持WebRTC的VPN
   - 绕过企业网络限制

### 如果是家庭网络
1. **路由器配置**：
   - 开启UPnP
   - 配置端口转发
   - 更新路由器固件

2. **ISP问题**：
   - 联系网络服务商
   - 检查是否有流量限制

### 如果是移动网络
1. **运营商限制**：
   - 某些运营商限制P2P流量
   - 尝试不同的APN设置

2. **网络类型**：
   - 4G/5G网络通常支持更好
   - 避免使用公共WiFi

## 🔧 临时解决方案

### 使用中继服务器
如果直连无法建立，强制使用TURN中继：

```javascript
// 在客户端配置中添加
iceTransportPolicy: "relay"
```

### 降级到HTTP
如果WebSocket连接失败，可以尝试HTTP长轮询：

```javascript
// 修改Janus连接URL
server: "http://*************:8088/janus"
```

## 📞 获取帮助

如果以上方案都无法解决问题，请提供：

1. **网络连通性测试结果截图**
2. **浏览器开发者工具的完整错误日志**
3. **网络环境描述**（企业/家庭/移动）
4. **防火墙/代理配置信息**

这将帮助进一步定位和解决问题。
