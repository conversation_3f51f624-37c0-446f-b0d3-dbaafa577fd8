# Janus WebRTC 配置详解

## 问题分析

### 原始问题
- ************ 能看到 *********** 的视频 ✅
- *********** 看不到别人的视频 ❌
- 抓包显示：服务器没有向 *********** 发送视频流

### 根本原因
Janus 的 NAT 配置导致内网客户端无法正确建立连接。

## 关键配置项解释

### 1. NAT 1:1 映射
```bash
nat_1_1_mapping = "*************"
```
- **作用**: 告诉 Janus 服务器的公网 IP
- **用途**: 用于公网客户端连接
- **问题**: 如果只提供公网 IP，内网客户端可能无法连接

### 2. 保留私有主机
```bash
keep_private_host = true    # 修改前是 false
```
- **作用**: 在提供公网 IP 的同时，也保留内网 IP
- **效果**: 客户端可以选择最佳的连接路径
- **重要性**: 这是解决内网连接问题的关键

### 3. ICE 强制列表
```bash
#ice_enforce_list = "*************"    # 已注释
```
- **作用**: 强制只使用指定的 IP 地址
- **问题**: 排除了内网 IP，导致内网客户端无法连接
- **解决**: 注释掉，允许使用所有可用的网络接口

### 4. ICE 忽略列表
```bash
ice_ignore_list = "vmnet"
```
- **作用**: 忽略虚拟网络接口
- **保留**: 这个配置是正确的

### 5. ICE 协商策略
```bash
ice_nomination = "aggressive"    # 修改前是 "regular"
```
- **aggressive**: 更快的连接建立
- **regular**: 更稳定但较慢的连接

## 修改总结

### 已修改的配置
1. `keep_private_host = true` - 保留内网 IP
2. `#ice_enforce_list = "*************"` - 注释掉强制限制
3. `ice_nomination = "aggressive"` - 更快的 ICE 协商

### 配置效果
- ✅ 公网客户端仍然可以正常连接
- ✅ 内网客户端现在也可以连接
- ✅ Janus 会提供多个 ICE 候选者，客户端选择最佳路径

## 网络拓扑说明

```
公网客户端 ←→ ************* (公网IP) ←→ Janus 服务器
                                        ↕
内网客户端 ←→ 内网IP (如 192.168.x.x) ←→ Janus 服务器
```

### 修改前
- Janus 只提供公网 IP 候选者
- 内网客户端无法连接到公网 IP
- ICE 候选者添加失败

### 修改后
- Janus 提供公网 IP + 内网 IP 候选者
- 内网客户端可以使用内网 IP 连接
- 公网客户端仍使用公网 IP 连接

## 测试验证

### 预期结果
1. **Janus 日志不再出现**:
   ```
   [WARN] Failed to add some remote candidates (added 0, expected 1)
   [ERR] srtp_err_status_fail
   ```

2. ************* 应该能够**:
   - 成功建立 ICE 连接
   - 接收到视频流
   - 在抓包中看到服务器发送的 RTP 包

3. **网络包分析应该显示**:
   - 服务器向 *********** 发送大量 RTP 包
   - 包大小通常 > 1000 字节（视频数据）
   - 持续的双向通信

## 故障排除

### 如果修改后仍有问题
1. **检查防火墙**: 确保 RTP 端口范围 (20000-40000) 开放
2. **检查路由**: 确保服务器能够路由到 ***********
3. **检查 NAT**: 中间路由器的 NAT 配置可能有问题

### 调试命令
```bash
# 检查 Janus 日志
tail -f /opt/janus/janus.log | grep -E "ICE|candidate|***********"

# 检查网络连接
netstat -an | grep ***********

# 抓包验证
tcpdump -i any host *********** and udp
```

## 配置文件位置
- 主配置: `/opt/janus/etc/janus/janus.jcfg`
- VideoRoom: `/opt/janus/etc/janus/janus.plugin.videoroom.jcfg`
- WebSocket: `/opt/janus/etc/janus/janus.transport.websockets.jcfg`
