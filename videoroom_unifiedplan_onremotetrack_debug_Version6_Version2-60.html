<!DOCTYPE html>
<html>
<head>
  <meta charset="UTF-8">
  <title>Janus VideoRoom Demo (onremotetrack debug版)</title>
  <script src="adapter-latest.js"></script>
  <script src="janus.js"></script>
  <style>
    #status { font-size:14px; color:#333; background:#f9f9f9; padding:6px; margin:10px 0; max-height:350px; overflow:auto; border:1px solid #eee;}
    #remotes video { margin:4px; border:1px solid #ccc;}
  </style>
</head>
<body>
  <h2>Janus VideoRoom Demo（onremotetrack调试版）</h2>
  <button id="start">加入房间</button>
  <div id="status"></div>
  <video id="myvideo" width="320" height="240" autoplay playsinline muted controls></video>
  <div id="remotes"></div>
  <script>
    let janus = null, videoroom = null, myid = null, mypvtid = null, feeds = [];

    function log(msg, ...args) {
      console.log(msg, ...args);
      const s = document.getElementById('status');
      s.innerHTML += `<br>${msg} ${args.map(a=>JSON.stringify(a)).join(' ')}`;
      s.scrollTop = s.scrollHeight;
    }
    function logVideoState(video, prefix) {
      if (!video) return;
      log(prefix+" video标签状态: width="+video.videoWidth+", height="+video.videoHeight+", readyState="+video.readyState);
      if (video.srcObject) {
        let tracks = video.srcObject.getVideoTracks();
        log(prefix+" video.srcObject videoTracks:", tracks.map(t=>({id:t.id, readyState:t.readyState, muted:t.muted})));
      } else {
        log(prefix+" video.srcObject为空");
      }
    }

    document.getElementById('start').onclick = function() {
      log("点击了加入房间按钮");
      Janus.init({
        debug: "all",
        callback: function() {
          log("Janus.init回调执行");
          janus = new Janus({
            server: "ws://192.168.1.203:8188",
            iceServers: [
              // 使用多个公共STUN服务器
              { urls: "stun:stun.l.google.com:19302" },
              { urls: "stun:stun1.l.google.com:19302" },
              { urls: "stun:stun2.l.google.com:19302" },
              { urls: "stun:stun3.l.google.com:19302" },
              { urls: "stun:stun4.l.google.com:19302" },
              { urls: "stun:stun.services.mozilla.com:3478" },
              // 如果自建TURN服务器不可用，可以临时使用公共服务
              // { urls: "turn:openrelay.metered.ca:80", username: "openrelayproject", credential: "openrelayproject" },
              // { urls: "turn:openrelay.metered.ca:443", username: "openrelayproject", credential: "openrelayproject" },
              // 自建TURN服务器（如果可用）
              { urls: "turn:60.255.197.32:3478", username: "hhzt", credential: "hhzt20130403" },
              { urls: "turn:60.255.197.32:3479", username: "hhzt", credential: "hhzt20130403" }
            ],
			// 强制使用TURN中继，解决单向连接问题
			//iceTransportPolicy: "relay",
			iceCandidatePoolSize: 10,
            success: function() {
              log("Janus连接成功");
              janus.attach({
                plugin: "janus.plugin.videoroom",
                success: function(pluginHandle) {
                  videoroom = pluginHandle;
                  log("VideoRoom插件attach成功");
                  let register = {
                    "request": "join",
                    "room": 1234,
                    "ptype": "publisher",
                    "display": "user-" + Janus.randomString(4)
                  };
                  videoroom.send({"message": register});
                  log("发送join房间请求", register);
                },
                error: function(error) {
                  log("Attach plugin error:", error);
                },
                onmessage: function(msg, jsep) {
                  log("收到onmessage事件", msg);
                  if(msg["videoroom"] === "joined") {
                    myid = msg["id"];
                    mypvtid = msg["private_id"];
                    log("房间加入成功，开始createOffer");
                    videoroom.createOffer({
                      media: { audio: true, video: true },
                      success: function(jsep) {
                        log("createOffer成功，发送publish", jsep);
                        let publish = { "request": "publish", "audio": true, "video": true };
                        videoroom.send({"message": publish, "jsep": jsep});
                        log("本地 SDP offer：", jsep.sdp);
                      },
                      error: function(error) {
                        log("WebRTC createOffer error:", error);
                      }
                    });
                    if(msg["publishers"]) {
                      log('房间内已有publishers:', msg["publishers"]);
                      for(let f of msg["publishers"]) {
                        newRemoteFeed(f.id, f.display);
                      }
                    }
                  }
                  if(msg["videoroom"] === "event" && msg["publishers"]) {
                    log('event发现新publishers:', msg["publishers"]);
                    for(let f of msg["publishers"]) {
                      newRemoteFeed(f.id, f.display);
                    }
                  }
                  if(jsep) {
                    log("收到远端jsep", jsep);
                    videoroom.handleRemoteJsep({jsep: jsep});
                    if(jsep.type === "answer") {
                      log("本地 SDP answer：", jsep.sdp);
                    }
                  }
                },
                onlocaltrack: function(track, mid, on) {
                  log("onlocaltrack", track, "mid:", mid, "on:", on);
                  if(!on) return;
                  let video = document.getElementById('myvideo');
                  if(!video.srcObject) video.srcObject = new MediaStream();
                  video.srcObject.addTrack(track);
                  log("本地video.srcObject已添加track", video.srcObject.getTracks());
                  track.onunmute = () => log("本地track "+track.kind+" id="+track.id+" onunmute");
                  track.onended = () => log("本地track "+track.kind+" id="+track.id+" onended");
                  video.muted = true;
                  video.controls = true;
                  video.play().then(()=>log("本地video play()成功")).catch(e=>log("本地video play()失败",e));
                  setTimeout(()=>logVideoState(video, "本地"), 1000);
                },
                onremotetrack: function(track, mid, on) {
                  log("onremotetrack", track, "mid:", mid, "on:", on);
                  if(!on) return;
                  // 只添加一次
                  let remotevideo = document.getElementById('remotevideo');
                  if(!remotevideo) {
                    remotevideo = document.createElement('video');
                    remotevideo.id = 'remotevideo';
                    remotevideo.autoplay = true;
                    remotevideo.playsInline = true;
                    remotevideo.muted = true;
                    remotevideo.width = 320;
                    remotevideo.height = 240;
                    remotevideo.controls = true;
                    document.getElementById('remotes').appendChild(remotevideo);
                  }
                  if(!remotevideo.srcObject) remotevideo.srcObject = new MediaStream();
                  remotevideo.srcObject.addTrack(track);
                  log("远端video.srcObject已添加track", remotevideo.srcObject.getTracks());
                  track.onunmute = () => log("远端track "+track.kind+" id="+track.id+" onunmute");
                  track.onended = () => log("远端track "+track.kind+" id="+track.id+" onended");
                  remotevideo.play().then(()=>log("远端video play()成功")).catch(e=>log("远端video play()失败",e));
                  setTimeout(()=>logVideoState(remotevideo, "远端"), 1000);
                },
                oncleanup: function() {
                  log("VideoRoom插件 oncleanup");
                }
              });
            }
          });
        }
      });
    };

    function newRemoteFeed(id, display) {
      let remoteFeed = null;
      let remoteStream = new MediaStream();
      janus.attach({
        plugin: "janus.plugin.videoroom",
        success: function(pluginHandle) {
          remoteFeed = pluginHandle;
          log("新远端订阅 attach 成功", display);
          let subscribe = {
            "request": "join",
            "room": 1234,
            "ptype": "subscriber",
            "feed": id,
            "private_id": mypvtid
          };
          remoteFeed.send({"message": subscribe});
          log("发送订阅请求", subscribe);
        },
        onmessage: function(msg, jsep) {
          log("远端feed onmessage", msg);
          if(jsep) {
            remoteFeed.createAnswer({
              jsep: jsep,
              media: { audioSend: false, videoSend: false },
              success: function(jsep) {
                let body = { "request": "start", "room": 1234 };
                remoteFeed.send({"message": body, "jsep": jsep});
                log("发送start订阅", body);
                log("远端 SDP answer：", jsep.sdp);
              },
              error: function(e) {
                log("remoteFeed.createAnswer失败", e);
              }
            });
          }
        },
        onremotetrack: function(track, mid, on) {
          log("订阅onremotetrack", track, "mid:", mid, "on:", on);
          if(!on) return;
          let vid = document.getElementById(`remotevideo-${id}`);
          if(!vid) {
            vid = document.createElement('video');
            vid.id = `remotevideo-${id}`;
            vid.autoplay = true;
            vid.playsInline = true;
            vid.muted = true;
            vid.width = 320;
            vid.height = 240;
            vid.controls = true;
            document.getElementById('remotes').appendChild(vid);
          }
          if(!vid.srcObject) vid.srcObject = new MediaStream();
          vid.srcObject.addTrack(track);
          log("订阅远端video.srcObject已添加track", vid.srcObject.getTracks());
          track.onunmute = () => log("订阅远端track "+track.kind+" id="+track.id+" onunmute");
          track.onended = () => log("订阅远端track "+track.kind+" id="+track.id+" onended");
          vid.play().then(()=>log("订阅远端video play()成功")).catch(e=>log("订阅远端video play()失败",e));
          setTimeout(()=>logVideoState(vid, `订阅远端-${id}`), 1000);
        },
        oncleanup: function() {
          log("远端feed oncleanup");
        }
      });
    }
  </script>
</body>
</html>