#!/bin/bash

# WebRTC服务重启脚本
# 用于重启Janus和TURN服务器

echo "=== WebRTC服务重启脚本 ==="
echo "当前时间: $(date)"
echo "当前目录: $(pwd)"

# 检查是否以root权限运行
if [ "$EUID" -ne 0 ]; then
    echo "请以root权限运行此脚本"
    echo "使用: sudo $0"
    exit 1
fi

# 停止现有服务
echo ""
echo "1. 停止现有服务..."

# 停止Janus
echo "停止Janus服务..."
pkill -f janus || echo "Janus未运行"

# 停止TURN服务器
echo "停止TURN服务器..."
pkill -f turnserver || echo "TURN服务器未运行"

# 等待进程完全停止
sleep 3

# 检查端口占用
echo ""
echo "2. 检查端口占用情况..."
echo "检查8188端口 (Janus WebSocket):"
netstat -tlnp | grep :8188 || echo "端口8188未被占用"

echo "检查3478端口 (TURN):"
netstat -tlnp | grep :3478 || echo "端口3478未被占用"

echo "检查20000-40000端口范围 (RTP):"
netstat -tlnp | grep -E ":(2[0-9]{4}|3[0-9]{4}|40000)" | head -5 || echo "RTP端口范围未被大量占用"

# 启动TURN服务器
echo ""
echo "3. 启动TURN服务器..."
if [ -f "./turnserver.conf" ]; then
    turnserver -c ./turnserver.conf -v &
    TURN_PID=$!
    echo "TURN服务器已启动，PID: $TURN_PID"
    sleep 2
    
    # 验证TURN服务器是否启动成功
    if ps -p $TURN_PID > /dev/null; then
        echo "TURN服务器启动成功"
    else
        echo "TURN服务器启动失败"
        exit 1
    fi
else
    echo "错误: 找不到turnserver.conf文件"
    exit 1
fi

# 启动Janus
echo ""
echo "4. 启动Janus服务器..."
if [ -f "./janus.jcfg" ]; then
    # 检查Janus可执行文件
    JANUS_BIN=$(which janus)
    if [ -z "$JANUS_BIN" ]; then
        # 尝试常见路径
        if [ -f "/opt/janus/bin/janus" ]; then
            JANUS_BIN="/opt/janus/bin/janus"
        elif [ -f "/usr/local/bin/janus" ]; then
            JANUS_BIN="/usr/local/bin/janus"
        else
            echo "错误: 找不到Janus可执行文件"
            echo "请确保Janus已正确安装"
            exit 1
        fi
    fi
    
    $JANUS_BIN -C ./janus.jcfg &
    JANUS_PID=$!
    echo "Janus服务器已启动，PID: $JANUS_PID"
    sleep 3
    
    # 验证Janus是否启动成功
    if ps -p $JANUS_PID > /dev/null; then
        echo "Janus服务器启动成功"
    else
        echo "Janus服务器启动失败"
        exit 1
    fi
else
    echo "错误: 找不到janus.jcfg文件"
    exit 1
fi

# 最终检查
echo ""
echo "5. 服务状态检查..."
echo "TURN服务器状态:"
ps aux | grep turnserver | grep -v grep || echo "TURN服务器未运行"

echo "Janus服务器状态:"
ps aux | grep janus | grep -v grep || echo "Janus服务器未运行"

echo "端口监听状态:"
echo "8188端口 (Janus WebSocket):"
netstat -tlnp | grep :8188

echo "3478端口 (TURN):"
netstat -tlnp | grep :3478

echo ""
echo "=== 服务重启完成 ==="
echo "请等待几秒钟让服务完全启动，然后测试连接"
echo ""
echo "测试建议:"
echo "1. 打开 test_webrtc_connectivity.html 进行连接测试"
echo "2. 检查防火墙设置，确保以下端口开放:"
echo "   - 8188 (Janus WebSocket)"
echo "   - 3478 (TURN)"
echo "   - 20000-40000 (RTP媒体流)"
echo "   - 49152-65535 (TURN relay)"
echo "3. 如果问题仍然存在，检查日志文件:"
echo "   - TURN日志: /var/log/turnserver.log"
echo "   - Janus日志: 控制台输出"
