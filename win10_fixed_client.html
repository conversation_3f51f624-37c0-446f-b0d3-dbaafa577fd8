<!DOCTYPE html>
<html>
<head>
  <title>Win10 修复版客户端</title>
  <style>
    body { font-family: Arial, sans-serif; margin: 20px; }
    video { width: 320px; height: 240px; border: 2px solid #000; margin: 10px; }
    button { padding: 10px 20px; margin: 5px; font-size: 16px; }
    #log { height: 300px; overflow-y: scroll; border: 1px solid #ccc; padding: 10px; font-family: monospace; font-size: 12px; }
    .status { padding: 10px; margin: 10px 0; border-radius: 5px; }
    .success { background-color: #d4edda; color: #155724; }
    .error { background-color: #f8d7da; color: #721c24; }
  </style>
</head>
<body>
  <h1>Win10 修复版 WebRTC 客户端</h1>
  <p><strong>专门针对 Windows 10 TURN 连接问题的修复版本</strong></p>
  
  <div id="status" class="status">准备测试...</div>
  
  <div>
    <button onclick="startWin10Test()">开始 Win10 修复测试</button>
    <button onclick="clearLog()">清除日志</button>
  </div>
  
  <div>
    <h3>本地视频</h3>
    <video id="localVideo" autoplay muted playsinline></video>
    
    <h3>远程视频</h3>
    <video id="remoteVideo" autoplay playsinline></video>
  </div>
  
  <div>
    <h3>调试日志</h3>
    <div id="log"></div>
  </div>

  <script src="adapter-latest.js"></script>
  <script src="janus.js"></script>
  
  <script>
    let janus = null;
    let videoroom = null;
    let localStream = null;
    let remoteFeed = null;
    
    function log(message) {
      const timestamp = new Date().toLocaleTimeString();
      const logDiv = document.getElementById('log');
      logDiv.innerHTML += `[${timestamp}] ${message}<br>`;
      logDiv.scrollTop = logDiv.scrollHeight;
      console.log(`[${timestamp}] ${message}`);
    }
    
    function updateStatus(message, type = 'info') {
      const statusDiv = document.getElementById('status');
      statusDiv.textContent = message;
      statusDiv.className = `status ${type}`;
      log(`状态: ${message}`);
    }
    
    function clearLog() {
      document.getElementById('log').innerHTML = '';
    }
    
    async function startWin10Test() {
      log('🔧 开始 Win10 专用修复测试');
      updateStatus('开始 Win10 修复测试...', 'info');
      
      try {
        // 获取摄像头
        log('📹 获取摄像头...');
        localStream = await navigator.mediaDevices.getUserMedia({
          video: { width: 640, height: 480 },
          audio: false
        });
        
        document.getElementById('localVideo').srcObject = localStream;
        log('✅ 摄像头获取成功');
        
        // 初始化 Janus
        log('🔧 初始化 Janus...');
        Janus.init({
          debug: "all",
          callback: function() {
            log('✅ Janus 初始化成功');
            connectToJanusWin10();
          }
        });
        
      } catch (error) {
        log('❌ 测试失败: ' + error.message);
        updateStatus('测试失败: ' + error.message, 'error');
      }
    }
    
    function connectToJanusWin10() {
      // Win10 专用配置 - 避免 TURN 连接问题
      const win10Config = {
        server: "ws://60.255.197.32:8188/",
        // Win10 优化的 ICE 配置
        iceServers: [
          // 优先使用 STUN，减少 TURN 依赖
          { urls: "stun:stun.l.google.com:19302" },
          { urls: "stun:stun1.l.google.com:19302" },
          { urls: "stun:stun.cloudflare.com:3478" },
          // TURN 作为备选
          { 
            urls: "turn:60.255.197.32:3478", 
            username: "hhzt", 
            credential: "hhzt20130403",
            credentialType: "password"
          }
        ],
        // Win10 兼容性设置
        iceTransportPolicy: "all",           // 允许所有传输方式
        bundlePolicy: "max-bundle",          // 最大化捆绑
        rtcpMuxPolicy: "require",            // 要求 RTCP 复用
        iceCandidatePoolSize: 10,            // 增加候选者池大小
        
        // Win10 特殊配置
        sdpSemantics: "unified-plan",        // 使用统一计划
        
        success: function() {
          log('✅ Janus 连接成功');
          updateStatus('Janus 连接成功', 'success');
          attachVideoRoomWin10();
        },
        error: function(error) {
          log('❌ Janus 连接失败: ' + error);
          updateStatus('Janus 连接失败: ' + error, 'error');
        },
        destroyed: function() {
          log('⚠️ Janus 连接已断开');
        }
      };
      
      janus = new Janus(win10Config);
    }
    
    function attachVideoRoomWin10() {
      janus.attach({
        plugin: "janus.plugin.videoroom",
        opaqueId: "win10-publisher-" + Janus.randomString(12),
        success: function(pluginHandle) {
          videoroom = pluginHandle;
          log('✅ VideoRoom 插件连接成功');
          
          // 加入房间
          const register = {
            request: "join",
            room: 1000,
            ptype: "publisher",
            display: "Win10-192.168.1.2-Fixed",
            pin: ""
          };
          
          videoroom.send({ message: register });
          log('📤 发送加入房间请求');
        },
        error: function(error) {
          log('❌ VideoRoom 连接失败: ' + error);
          updateStatus('VideoRoom 连接失败: ' + error, 'error');
        },
        iceState: function(state) {
          log(`🧊 ICE 状态: ${state}`);
          if (state === 'connected') {
            updateStatus('ICE 连接成功', 'success');
          } else if (state === 'failed') {
            updateStatus('ICE 连接失败', 'error');
            // Win10 重连机制
            log('🔄 Win10 ICE 失败，尝试重连...');
            setTimeout(() => {
              if (videoroom) {
                videoroom.hangup();
                setTimeout(attachVideoRoomWin10, 3000);
              }
            }, 2000);
          }
        },
        mediaState: function(medium, on) {
          log(`📺 媒体状态: ${medium} ${on ? '开启' : '关闭'}`);
        },
        webrtcState: function(on) {
          log(`🔗 WebRTC 状态: ${on ? '已连接' : '已断开'}`);
          if (on) {
            updateStatus('WebRTC 连接成功', 'success');
          } else {
            updateStatus('WebRTC 连接断开', 'error');
            // Win10 特殊处理：不立即重连，等待更长时间
            log('⏳ Win10 WebRTC 断开，等待重新连接...');
          }
        },
        onmessage: function(msg, jsep) {
          log('📨 收到消息: ' + JSON.stringify(msg));
          
          if (msg["videoroom"] === "joined") {
            log('✅ 已加入房间');
            updateStatus('已加入房间，开始发布流...', 'success');
            
            // Win10 优化的 offer 创建
            const offerOptions = {
              media: {
                audioRecv: false,
                videoRecv: false,
                audioSend: false,
                videoSend: true,
                data: false
              },
              stream: localStream,
              // Win10 特殊选项
              trickle: true,
              iceRestart: false,  // 避免 ICE 重启问题
              success: function(jsep) {
                log('✅ 创建 offer 成功');
                const publish = {
                  request: "configure",
                  audio: false,
                  video: true,
                  bitrate: 1000000  // 适中的比特率
                };
                videoroom.send({ message: publish, jsep: jsep });
              },
              error: function(error) {
                log('❌ 创建 offer 失败: ' + error);
                updateStatus('创建 offer 失败: ' + error, 'error');
              }
            };
            
            videoroom.createOffer(offerOptions);
            
            // 检查是否有其他发布者
            if (msg["publishers"]) {
              msg["publishers"].forEach(publisher => {
                if (publisher["id"] !== msg["id"]) {
                  log(`🔍 发现其他发布者: ${publisher["display"]} (${publisher["id"]})`);
                  // Win10 延迟订阅，避免连接冲突
                  setTimeout(() => {
                    subscribeToFeedWin10(publisher["id"], publisher["display"]);
                  }, 5000);
                }
              });
            }
          } else if (msg["videoroom"] === "event") {
            if (msg["configured"] === "ok") {
              log('✅ 发布配置成功');
              updateStatus('本地流发布成功', 'success');
            }
            
            if (msg["publishers"]) {
              msg["publishers"].forEach(publisher => {
                log(`🔍 新发布者: ${publisher["display"]} (${publisher["id"]})`);
                // Win10 延迟订阅
                setTimeout(() => {
                  subscribeToFeedWin10(publisher["id"], publisher["display"]);
                }, 5000);
              });
            }
          }
          
          if (jsep) {
            log('📥 处理 SDP: ' + jsep.type);
            videoroom.handleRemoteJsep({ jsep: jsep });
          }
        }
      });
    }
    
    function subscribeToFeedWin10(id, display) {
      log(`🔄 Win10 订阅 ${display} (ID: ${id})`);
      
      janus.attach({
        plugin: "janus.plugin.videoroom",
        opaqueId: "win10-subscriber-" + Janus.randomString(12),
        success: function(pluginHandle) {
          remoteFeed = pluginHandle;
          log('✅ Win10 订阅插件连接成功');
          
          const subscribe = {
            request: "join",
            room: 1000,
            ptype: "subscriber",
            feed: id,
            private_id: Math.floor(Math.random() * 1000000),
            offer_audio: false,
            offer_video: true,
            pin: ""
          };
          
          remoteFeed.send({ message: subscribe });
        },
        error: function(error) {
          log('❌ Win10 订阅失败: ' + error);
          // Win10 重试机制
          setTimeout(() => {
            log('🔄 Win10 重试订阅...');
            subscribeToFeedWin10(id, display);
          }, 10000);
        },
        iceState: function(state) {
          log(`🧊 Win10 订阅 ICE 状态: ${state}`);
          if (state === 'connected') {
            updateStatus('订阅 ICE 连接成功', 'success');
          }
        },
        webrtcState: function(on) {
          log(`🔗 Win10 订阅 WebRTC 状态: ${on ? '已连接' : '已断开'}`);
          if (on) {
            updateStatus('Win10 订阅连接成功！', 'success');
          } else {
            log('⚠️ Win10 订阅断开，但不重连，保持等待状态');
          }
        },
        onremotetrack: function(track, mid, on) {
          log(`🎬 Win10 收到远程轨道: ${track.kind}, mid: ${mid}, on: ${on}`);
          
          if (track.kind === 'video' && on) {
            const remoteVideo = document.getElementById('remoteVideo');
            const stream = new MediaStream([track]);
            remoteVideo.srcObject = stream;
            
            remoteVideo.onloadedmetadata = function() {
              log('✅ Win10 远程视频元数据加载完成');
              remoteVideo.play().then(() => {
                log('🎉 Win10 远程视频开始播放！');
                updateStatus('Win10 视频播放成功！', 'success');
              }).catch(e => {
                log('❌ Win10 远程视频播放失败: ' + e.message);
              });
            };
          }
        },
        onmessage: function(msg, jsep) {
          log('📨 Win10 订阅消息: ' + JSON.stringify(msg));
          
          if (jsep) {
            log('📥 Win10 处理订阅 SDP: ' + jsep.type);
            remoteFeed.createAnswer({
              jsep: jsep,
              media: { audioSend: false, videoSend: false },
              success: function(jsep) {
                log('✅ Win10 订阅应答创建成功');
                const body = { request: "start", room: 1000 };
                remoteFeed.send({ message: body, jsep: jsep });
              },
              error: function(error) {
                log('❌ Win10 订阅应答创建失败: ' + error);
              }
            });
          }
        }
      });
    }
  </script>
</body>
</html>
