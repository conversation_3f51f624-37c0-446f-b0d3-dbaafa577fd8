# Janus WebRTC 快速启动指南

## 问题解决

您遇到的启动错误是因为STUN服务器连接失败。我已经修复了配置文件。

## 修复内容

1. **注释掉STUN服务器配置** - 避免启动时的连接检查失败
2. **注释掉TURN服务器配置** - 简化初始配置
3. **添加了 `ignore_unreachable_ice_server = true`** - 允许在外部服务器不可达时启动

## 启动步骤

### 方法1：使用修复后的配置文件

```bash
cd /opt/janus
bin/janus -C /root/janus/janus.jcfg
```

### 方法2：使用简化配置文件

```bash
cd /opt/janus
bin/janus -C /root/janus/janus_simple.jcfg
```

## 验证启动成功

启动成功后，您应该看到类似以下的输出：

```
Janus version: 1301 (1.3.1)
...
---------------------------------------------------
  Starting <PERSON><PERSON><PERSON> (WebRTC Server) v1.3.1
---------------------------------------------------
...
WebServer started (port 8088)...
Admin/monitor webserver started (port 7088)...
WebSockets server started (port 8188)...
```

## 测试连接

1. **检查端口监听**：
   ```bash
   netstat -tlnp | grep 8188
   ```

2. **测试WebSocket连接**：
   ```bash
   curl -i -N -H "Connection: Upgrade" \
        -H "Upgrade: websocket" \
        -H "Sec-WebSocket-Key: test" \
        -H "Sec-WebSocket-Version: 13" \
        http://60.255.197.32:8188/
   ```

3. **使用浏览器测试**：
   打开 `videoroom_unifiedplan_onremotetrack_debug_Version6_Version2-60.html`

## 如果仍有问题

### 检查防火墙

```bash
# 检查防火墙状态
systemctl status firewalld
# 或
ufw status

# 开放必要端口
firewall-cmd --permanent --add-port=8188/tcp
firewall-cmd --permanent --add-port=20000-40000/udp
firewall-cmd --reload
```

### 检查SELinux

```bash
# 检查SELinux状态
sestatus

# 如果启用了SELinux，可能需要设置策略
setsebool -P httpd_can_network_connect 1
```

### 查看详细日志

启动时添加更多调试信息：

```bash
cd /opt/janus
bin/janus -C /root/janus/janus.jcfg -L 7
```

## 逐步启用功能

一旦基本功能工作，您可以逐步启用更多功能：

### 1. 启用STUN服务器

在 `janus.jcfg` 中取消注释：
```
stun_server = "stun.l.google.com"
stun_port = 19302
```

### 2. 启用TURN服务器

确保TURN服务器正在运行，然后在 `janus.jcfg` 中取消注释：
```
turn_server = "60.255.197.32"
turn_port = 3478
turn_type = "udp"
turn_user = "hhzt"
turn_pwd = "hhzt20130403"
```

### 3. 客户端配置

在客户端HTML文件中，根据需要调整ICE服务器配置。

## 常见问题

### Q: 启动时提示找不到插件文件夹
**A**: 检查路径配置，确保 `/opt/janus/lib/janus/plugins` 存在

### Q: 端口8188被占用
**A**: 使用 `netstat -tlnp | grep 8188` 查找占用进程，或修改配置使用其他端口

### Q: 权限问题
**A**: 确保以适当权限运行，可能需要 `sudo`

## 下一步

1. 先让Janus基本功能启动成功
2. 测试WebSocket连接
3. 测试基本的视频通话功能
4. 逐步启用STUN/TURN功能
5. 优化网络配置解决单向视频问题
