<!DOCTYPE html>
<html>
<head>
    <title>Simple VideoRoom Client</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .container { max-width: 1200px; margin: 0 auto; }
        .video-container { display: flex; flex-wrap: wrap; gap: 20px; margin: 20px 0; }
        .video-box { border: 2px solid #ccc; padding: 10px; border-radius: 5px; }
        video { width: 320px; height: 240px; background: #000; }
        .controls { margin: 20px 0; }
        button { padding: 10px 20px; margin: 5px; font-size: 16px; cursor: pointer; }
        .button-primary { background: #007bff; color: white; border: none; border-radius: 4px; }
        .button-danger { background: #dc3545; color: white; border: none; border-radius: 4px; }
        #log { height: 300px; overflow-y: scroll; border: 1px solid #ccc; padding: 10px; 
               font-family: monospace; font-size: 12px; background: #f8f9fa; }
        .status { padding: 10px; margin: 10px 0; border-radius: 4px; }
        .status.success { background: #d4edda; color: #155724; border: 1px solid #c3e6cb; }
        .status.error { background: #f8d7da; color: #721c24; border: 1px solid #f5c6cb; }
        .status.info { background: #d1ecf1; color: #0c5460; border: 1px solid #bee5eb; }
    </style>
</head>
<body>
    <div class="container">
        <h1>Simple VideoRoom Client</h1>
        <div id="status" class="status info">准备连接...</div>
        
        <div class="controls">
            <button id="start" class="button-primary" onclick="start()">开始</button>
            <button id="stop" class="button-danger" onclick="stop()" disabled>停止</button>
            <span style="margin-left: 20px;">房间号: <strong>1000</strong></span>
        </div>
        
        <div class="video-container">
            <div class="video-box">
                <h3>本地视频</h3>
                <video id="myvideo" autoplay playsinline muted="muted"></video>
            </div>
            <div class="video-box">
                <h3>远程视频</h3>
                <video id="remotevideo" autoplay playsinline></video>
            </div>
        </div>
        
        <div>
            <h3>调试日志</h3>
            <div id="log"></div>
        </div>
    </div>

    <script src="adapter-latest.js"></script>
    <script src="janus.js"></script>
    
    <script>
        // 全局变量
        var janus = null;
        var sfutest = null;
        var opaqueId = "videoroomtest-" + Janus.randomString(12);
        var mystream = null;
        var myid = null;
        var myusername = null;
        var feeds = [];
        var bitrateTimer = [];

        // 日志函数
        function log(msg) {
            var time = new Date().toLocaleTimeString();
            document.getElementById('log').innerHTML += '[' + time + '] ' + msg + '<br>';
            document.getElementById('log').scrollTop = document.getElementById('log').scrollHeight;
            console.log(msg);
        }

        // 状态更新
        function updateStatus(msg, type) {
            var status = document.getElementById('status');
            status.textContent = msg;
            status.className = 'status ' + (type || 'info');
            log('状态: ' + msg);
        }

        // 开始函数
        function start() {
            document.getElementById('start').disabled = true;
            updateStatus('正在初始化 Janus...', 'info');
            
            // 初始化 Janus
            Janus.init({
                debug: "all",
                callback: function() {
                    log('Janus 初始化成功');
                    
                    // 创建 Janus 会话
                    janus = new Janus({
                        server: 'ws://60.255.197.32:8188/',
                        success: function() {
                            log('Janus 连接成功');
                            updateStatus('Janus 连接成功', 'success');
                            
                            // 连接到 VideoRoom 插件
                            janus.attach({
                                plugin: "janus.plugin.videoroom",
                                opaqueId: opaqueId,
                                success: function(pluginHandle) {
                                    sfutest = pluginHandle;
                                    log('VideoRoom 插件连接成功');
                                    updateStatus('正在获取摄像头...', 'info');
                                    
                                    // 获取用户媒体
                                    navigator.mediaDevices.getUserMedia({
                                        audio: true,  // 改为 true，匹配服务器配置
                                        video: true
                                    }).then(function(stream) {
                                        log('摄像头获取成功');
                                        mystream = stream;
                                        document.getElementById('myvideo').srcObject = stream;
                                        
                                        // 加入房间
                                        joinRoom();
                                    }).catch(function(error) {
                                        log('摄像头获取失败: ' + error);
                                        updateStatus('摄像头获取失败: ' + error, 'error');
                                    });
                                },
                                error: function(error) {
                                    log('VideoRoom 插件连接失败: ' + error);
                                    updateStatus('VideoRoom 插件连接失败: ' + error, 'error');
                                },
                                onmessage: function(msg, jsep) {
                                    log('收到 VideoRoom 消息: ' + JSON.stringify(msg));
                                    var event = msg["videoroom"];
                                    
                                    if(event != undefined && event != null) {
                                        if(event === "joined") {
                                            // 成功加入房间
                                            myid = msg["id"];
                                            log('成功加入房间，ID: ' + myid);
                                            updateStatus('已加入房间，正在发布视频...', 'success');
                                            
                                            // 发布本地流
                                            publishOwnFeed(true);
                                            
                                            // 订阅现有的发布者
                                            if(msg["publishers"] !== undefined && msg["publishers"] !== null) {
                                                var list = msg["publishers"];
                                                for(var f in list) {
                                                    var id = list[f]["id"];
                                                    var display = list[f]["display"];
                                                    log('发现发布者: ' + display + ' (ID: ' + id + ')');
                                                    newRemoteFeed(id, display);
                                                }
                                            }
                                        } else if(event === "event") {
                                            if(msg["publishers"] !== undefined && msg["publishers"] !== null) {
                                                var list = msg["publishers"];
                                                for(var f in list) {
                                                    var id = list[f]["id"];
                                                    var display = list[f]["display"];
                                                    log('新发布者加入: ' + display + ' (ID: ' + id + ')');
                                                    newRemoteFeed(id, display);
                                                }
                                            } else if(msg["leaving"] !== undefined && msg["leaving"] !== null) {
                                                var leaving = msg["leaving"];
                                                log('发布者离开: ' + leaving);
                                                var remoteFeed = null;
                                                for(var i=1; i<6; i++) {
                                                    if(feeds[i] != null && feeds[i] != undefined && feeds[i].rfid == leaving) {
                                                        remoteFeed = feeds[i];
                                                        break;
                                                    }
                                                }
                                                if(remoteFeed != null) {
                                                    remoteFeed.detach();
                                                }
                                            }
                                        }
                                    }
                                    
                                    if(jsep !== undefined && jsep !== null) {
                                        log('处理 SDP: ' + jsep.type);
                                        sfutest.handleRemoteJsep({jsep: jsep});
                                    }
                                },
                                onlocalstream: function(stream) {
                                    log('本地流已准备');
                                },
                                onremotestream: function(stream) {
                                    // 这个在 VideoRoom 中不会被调用
                                },
                                oncleanup: function() {
                                    log('清理本地流');
                                    mystream = null;
                                }
                            });
                        },
                        error: function(error) {
                            log('Janus 连接失败: ' + error);
                            updateStatus('Janus 连接失败: ' + error, 'error');
                        },
                        destroyed: function() {
                            log('Janus 连接已断开');
                        }
                    });
                }
            });
        }

        // 加入房间
        function joinRoom() {
            var register = {
                "request": "join",
                "room": 1000,
                "ptype": "publisher",
                "display": "用户-" + Janus.randomString(6)
            };
            sfutest.send({"message": register});
        }

        // 发布本地流
        function publishOwnFeed(useAudio) {
            sfutest.createOffer({
                media: {
                    audioRecv: false,
                    videoRecv: false,
                    audioSend: useAudio,
                    videoSend: true
                },
                stream: mystream,
                success: function(jsep) {
                    log('创建 offer 成功');
                    var publish = { "request": "configure", "audio": useAudio, "video": true };
                    sfutest.send({"message": publish, "jsep": jsep});
                },
                error: function(error) {
                    log('创建 offer 失败: ' + error);
                    updateStatus('发布失败: ' + error, 'error');
                }
            });
        }

        // 新的远程流
        function newRemoteFeed(id, display) {
            var remoteFeed = null;
            janus.attach({
                plugin: "janus.plugin.videoroom",
                opaqueId: opaqueId,
                success: function(pluginHandle) {
                    remoteFeed = pluginHandle;
                    remoteFeed.simulcastStarted = false;
                    remoteFeed.rfid = id;
                    remoteFeed.rfdisplay = display;
                    feeds[id] = remoteFeed;
                    log('订阅插件连接成功，准备订阅 ' + display);
                    
                    var subscribe = {
                        "request": "join",
                        "room": 1000,
                        "ptype": "subscriber",
                        "feed": id,
                        "streams": [
                            { "feed": id, "mid": "video", "send": true }
                        ]
                    };
                    remoteFeed.send({"message": subscribe});
                },
                error: function(error) {
                    log('订阅插件连接失败: ' + error);
                },
                onmessage: function(msg, jsep) {
                    var event = msg["videoroom"];
                    if(event != undefined && event != null) {
                        if(event === "attached") {
                            log('成功订阅 ' + remoteFeed.rfdisplay);
                            updateStatus('正在接收 ' + remoteFeed.rfdisplay + ' 的视频...', 'info');
                        }
                    }
                    if(jsep !== undefined && jsep !== null) {
                        log('处理订阅 SDP: ' + jsep.type);
                        remoteFeed.createAnswer({
                            jsep: jsep,
                            media: { audioSend: false, videoSend: false, audioRecv: true, videoRecv: true },
                            success: function(jsep) {
                                var body = { "request": "start", "room": 1000 };
                                remoteFeed.send({"message": body, "jsep": jsep});
                            },
                            error: function(error) {
                                log('订阅应答失败: ' + error);
                            }
                        });
                    }
                },
                onlocalstream: function(stream) {
                    // 订阅者不会有本地流
                },
                onremotestream: function(stream) {
                    log('收到远程流: ' + remoteFeed.rfdisplay);
                    log('远程流轨道数量: ' + stream.getTracks().length);
                    stream.getTracks().forEach(function(track, index) {
                        log('轨道 ' + index + ': ' + track.kind + ', enabled: ' + track.enabled + ', readyState: ' + track.readyState);
                    });

                    document.getElementById('remotevideo').srcObject = stream;
                    updateStatus('正在播放 ' + remoteFeed.rfdisplay + ' 的视频', 'success');

                    // 监听视频播放事件
                    var video = document.getElementById('remotevideo');
                    video.onloadedmetadata = function() {
                        log('远程视频元数据加载完成: ' + video.videoWidth + 'x' + video.videoHeight);
                    };
                    video.onplay = function() {
                        log('远程视频开始播放');
                    };
                    video.onerror = function(e) {
                        log('远程视频播放错误: ' + e.message);
                    };
                },
                iceState: function(state) {
                    log('远程流 ICE 状态: ' + state + ' (' + remoteFeed.rfdisplay + ')');
                },
                webrtcState: function(on) {
                    log('远程流 WebRTC 状态: ' + (on ? '已连接' : '已断开') + ' (' + remoteFeed.rfdisplay + ')');
                    if (!on) {
                        log('⚠️ 远程流 WebRTC 断开，这可能导致视频停止');
                    }
                },
                oncleanup: function() {
                    log('清理远程流: ' + remoteFeed.rfdisplay);
                    document.getElementById('remotevideo').srcObject = null;
                }
            });
        }

        // 停止函数
        function stop() {
            document.getElementById('stop').disabled = true;
            document.getElementById('start').disabled = false;
            
            if(janus != null) {
                janus.destroy();
                janus = null;
            }
            
            if(mystream != null) {
                mystream.getTracks().forEach(function(track) {
                    track.stop();
                });
                mystream = null;
            }
            
            document.getElementById('myvideo').srcObject = null;
            document.getElementById('remotevideo').srcObject = null;
            
            feeds = [];
            updateStatus('已停止', 'info');
        }

        // 页面卸载时清理
        window.addEventListener('beforeunload', function() {
            if(janus != null) {
                janus.destroy();
            }
        });
    </script>
</body>
</html>
